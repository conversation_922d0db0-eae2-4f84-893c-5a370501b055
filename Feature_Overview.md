# ERPSaaS: Feature Overview and Functional Specification

This document provides a detailed overview of the features and functionality of the ERPSaaS application.

## 1. Core Concepts

The application is a multi-tenant, double-entry accounting system built on the TALL stack (Tailwind CSS, Alpine.js, Laravel, Livewire) with a Filament-powered admin panel.

### 1.1. Multi-Tenancy

The application is designed to support multiple companies within a single installation. Each company's data is isolated, ensuring privacy and security.

### 1.2. Double-Entry Accounting

The system is built around the principles of double-entry bookkeeping, ensuring that all financial transactions are accurately recorded and balanced.

## 2. Feature Breakdown

### 2.1. Company and User Management

-   **Company Management:** Users can create, manage, and switch between multiple companies.
-   **User Management:** The system supports user invitations and role-based access control within each company.
-   **Team Management:** The application includes the concept of employees and departments, allowing for the organization of users into teams.

### 2.2. Accounting Core

-   **Chart of Accounts:** A customizable and hierarchical chart of accounts with various account subtypes.
-   **Journal Entries:** Manual creation of journal entries for adjustments and other financial events.
-   **Transactions:** A centralized system for recording all financial transactions.
-   **Budgets:** The application supports budget creation, allocation, and tracking.

### 2.3. Sales and Receivables

-   **Clients and Contacts:** Management of clients and their associated contacts.
-   **Estimates:** Creation and management of estimates for potential sales.
-   **Invoices:** Generation of both standard and recurring invoices.
-   **Offerings:** The ability to define and manage products and services that can be added to invoices and bills.

### 2.4. Purchases and Payables

-   **Vendors and Contacts:** Management of vendors and their associated contacts.
-   **Bills:** Recording and management of bills from vendors.

### 2.5. Banking and Financial Institutions

-   **Bank Accounts:** Management of company bank accounts.
-   **Plaid Integration:**
    -   **Connected Bank Accounts:** Linking of external bank accounts through Plaid.
    -   **Institution Management:** The application stores information about financial institutions.

### 2.6. Financial Reporting

-   **Standard Reports:** A comprehensive suite of standard financial reports, including the Balance Sheet, Income Statement, Cash Flow Statement, and Trial Balance.
-   **Aging Reports:** Accounts Receivable and Accounts Payable aging reports.
-   **Entity-Specific Reports:** Reports on client and vendor balances and payment performance.
-   **Account-Specific Reports:** Reports on account balances and transactions.

### 2.7. Settings and Configuration

-   **Company Defaults:** Configuration of default settings for various aspects of the application.
-   **Company Profile:** Management of the company's profile information.
-   **Currency Management:** The application supports multiple currencies and can be configured to use live exchange rates.
-   **Document Defaults:** Configuration of default settings for documents like invoices and bills.
-   **Localization:** The application supports multiple languages and timezones.

## 3. Technical Stack

-   **Backend:** PHP 8.2+, Laravel 11+
-   **Frontend:** TALL Stack (Tailwind CSS, Alpine.js, Laravel, Livewire)
-   **Admin Panel:** Filament
-   **Database:** Not specified, but compatible with Laravel's supported databases (e.g., MySQL, PostgreSQL).
-   **Frontend Tooling:** Vite
-   **Testing:** Pest

## 4. Functional Specification

This section provides a more detailed functional specification for the ERPSaaS application.

### 4.1. User Management and Authentication

-   **User Registration and Login:** The application provides a standard email/password authentication system.
-   **Multi-Company Access:** Users can be associated with multiple companies and switch between them.
-   **Role-Based Access Control (RBAC):** The system should enforce a role-based access control system to manage user permissions. While the exact roles are not defined in the provided files, a typical setup would include roles like:
    -   **Administrator:** Full access to all features and settings.
    -   **Accountant:** Access to all accounting and reporting features.
    -   **Bookkeeper:** Access to data entry features like creating invoices and recording transactions.
    -   **Read-Only:** Access to view reports and financial data without the ability to make changes.

### 4.2. Company Setup and Configuration

-   **Company Creation:** Users can create new companies by providing basic information such as the company name, currency, and fiscal year.
-   **Company Settings:** Each company has a dedicated settings page where users can configure:
    -   **General Information:** Company name, address, and contact information.
    -   **Financial Settings:** Default currency, fiscal year-end, and accounting method (accrual or cash).
    -   **Localization:** Language and timezone.

### 4.3. Chart of Accounts

-   **Account Creation and Management:** Users can create, edit, and delete accounts in the chart of accounts.
-   **Account Types:** The system supports standard account types, including:
    -   Assets
    -   Liabilities
    -   Equity
    -   Revenue
    -   Expenses
-   **Account Hierarchy:** Accounts can be organized in a hierarchical structure with parent-child relationships.

### 4.4. Transaction Management

-   **Manual Journal Entries:** Users can create manual journal entries to record transactions that don't fit into other categories.
-   **Invoice and Bill Management:** The system should provide a way to create, send, and track invoices and bills.
-   **Transaction Categorization:** All transactions must be categorized by assigning them to the appropriate accounts in the chart of accounts.

### 4.5. Financial Reporting

-   **Report Generation:** Users can generate a variety of financial reports for a specified date range.
-   **Report Customization:** Some reports may offer customization options, such as filtering by account or entity.
-   **PDF Export:** All reports can be exported to PDF for printing or sharing.

### 4.6. Plaid Integration

-   **Bank Account Linking:** Users can link their bank accounts to the application using Plaid Link.
-   **Automatic Transaction Import:** The system automatically imports transactions from linked bank accounts.
-   **Transaction Reconciliation:** The application should provide a way to reconcile imported bank transactions with the transactions recorded in the system.

### 4.7. Live Currency

-   **Real-time Exchange Rates:** The system can be configured to fetch real-time exchange rates from an external API.
-   **Multi-Currency Transactions:** Users can record transactions in different currencies, and the system will automatically convert them to the company's base currency.

### 4.8. API

The application exposes a limited API for specific functionalities:

-   **`GET /api/user`:** Retrieves the authenticated user's information. Requires Sanctum authentication.
-   **`POST /api/plaid/webhook`:** Handles incoming webhooks from Plaid for transaction updates. This endpoint is not authenticated as it is called by an external service.

### 4.9. Web Routes

-   **`GET /`:** Redirects to the default Filament panel.
-   **`GET /documents/{documentType}/{id}/print`:** Generates a printable PDF for a specific document. This route requires authentication and uses a middleware to allow the document to be displayed in an iframe.
