<?php

namespace App\Providers;

use App\Services\SubscriptionService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class SubscriptionServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(SubscriptionService::class);
    }

    public function boot(): void
    {
        $this->registerGates();
    }

    private function registerGates(): void
    {
        Gate::define('access-advanced-reporting', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'advanced_reporting');
        });

        Gate::define('access-recurring-invoices', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'recurring_invoices');
        });

        Gate::define('access-multi-currency', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'multi_currency');
        });

        Gate::define('access-budget-management', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'budget_management');
        });

        Gate::define('access-custom-branding', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'custom_branding');
        });

        Gate::define('access-data-export', function ($user) {
            return app(SubscriptionService::class)->canAccessFeature($user, 'data_export');
        });

        Gate::define('create-company', function ($user) {
            return !app(SubscriptionService::class)->hasReachedLimit($user, 'companies');
        });

        Gate::define('invite-user', function ($user) {
            return !app(SubscriptionService::class)->hasReachedLimit($user, 'users_per_company');
        });

        Gate::define('create-bank-account', function ($user) {
            return !app(SubscriptionService::class)->hasReachedLimit($user, 'bank_accounts');
        });
    }
}
