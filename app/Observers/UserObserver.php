<?php

namespace App\Observers;

use App\Models\User;
use App\Services\SubscriptionService;

class UserObserver
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Start trial for new users (but not in demo environment)
        if (!is_demo_environment()) {
            // Start trial automatically for new users
            try {
                $this->subscriptionService->startTrial($user);
            } catch (\Exception $e) {
                // Log error but don't fail user creation
                logger('Failed to start trial for user: ' . $e->getMessage());
            }
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // Cancel any active subscriptions when user is deleted
        if ($user->hasStripeId()) {
            try {
                $subscription = $user->subscription('default');
                if ($subscription && $subscription->active()) {
                    $subscription->cancelNow();
                }
            } catch (\Exception $e) {
                // Log error but don't fail the deletion
                logger('Failed to cancel subscription for deleted user: ' . $e->getMessage());
            }
        }
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        // Same as deleted
        $this->deleted($user);
    }
}
