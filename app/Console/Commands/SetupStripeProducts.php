<?php

namespace App\Console\Commands;

use App\Enums\SubscriptionPlan;
use Illuminate\Console\Command;
use Stripe\Price;
use Stripe\Product;
use Stripe\Stripe;

class SetupStripeProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:setup-products {--force : Force recreation of products}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up Stripe products and prices for subscription plans';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!config('services.stripe.secret')) {
            $this->error('Stripe secret key not configured. Please set STRIPE_SECRET in your .env file.');
            return 1;
        }

        Stripe::setApiKey(config('services.stripe.secret'));

        $this->info('Setting up Stripe products and prices...');

        foreach (SubscriptionPlan::getAvailablePlans() as $plan) {
            $this->setupPlan($plan);
        }

        $this->info('Stripe products and prices setup completed!');
        $this->newLine();
        $this->info('Please update your .env file with the following price IDs:');

        return 0;
    }

    private function setupPlan(SubscriptionPlan $plan): void
    {
        $this->info("Setting up {$plan->label()} plan...");

        try {
            // Create or get product
            $product = $this->createProduct($plan);

            // Create monthly price
            $monthlyPrice = $this->createPrice($product->id, $plan->monthlyPrice(), 'month');
            $this->line("Monthly price ID: {$monthlyPrice->id}");

            // Create yearly price
            $yearlyPrice = $this->createPrice($product->id, $plan->yearlyPrice(), 'year');
            $this->line("Yearly price ID: {$yearlyPrice->id}");

            // Output environment variables
            $envPrefix = strtoupper($plan->value);
            $this->comment("STRIPE_{$envPrefix}_MONTHLY_PRICE_ID={$monthlyPrice->id}");
            $this->comment("STRIPE_{$envPrefix}_YEARLY_PRICE_ID={$yearlyPrice->id}");

        } catch (\Exception $e) {
            $this->error("Failed to setup {$plan->label()} plan: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function createProduct(SubscriptionPlan $plan): Product
    {
        $productId = "erpsaas-{$plan->value}";

        try {
            // Try to retrieve existing product
            $product = Product::retrieve($productId);

            if ($this->option('force')) {
                $this->line("Updating existing product: {$product->name}");
                $product = Product::update($productId, [
                    'name' => "ERPSaaS {$plan->label()}",
                    'description' => $plan->description(),
                ]);
            } else {
                $this->line("Using existing product: {$product->name}");
            }

        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Product doesn't exist, create it
            $this->line("Creating new product: ERPSaaS {$plan->label()}");
            $product = Product::create([
                'id' => $productId,
                'name' => "ERPSaaS {$plan->label()}",
                'description' => $plan->description(),
            ]);
        }

        return $product;
    }

    private function createPrice(string $productId, int $amount, string $interval): Price
    {
        $priceId = "erpsaas-{$productId}-{$interval}";

        try {
            // Try to retrieve existing price
            $price = Price::retrieve($priceId);
            $this->line("Using existing {$interval}ly price: {$price->id}");

        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Price doesn't exist, create it
            $this->line("Creating new {$interval}ly price...");
            $price = Price::create([
                'id' => $priceId,
                'product' => $productId,
                'unit_amount' => $amount,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $interval,
                ],
            ]);
        }

        return $price;
    }
}
