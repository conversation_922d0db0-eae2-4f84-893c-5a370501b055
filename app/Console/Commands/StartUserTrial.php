<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Console\Command;

class StartUserTrial extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:start-trial {user? : User ID or email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start a trial subscription for a user';

    /**
     * Execute the console command.
     */
    public function handle(SubscriptionService $subscriptionService)
    {
        $userInput = $this->argument('user');

        if ($userInput) {
            // Start trial for specific user
            $user = is_numeric($userInput)
                ? User::find($userInput)
                : User::where('email', $userInput)->first();

            if (!$user) {
                $this->error('User not found.');
                return 1;
            }

            if ($subscriptionService->isOnTrial($user) || $user->subscription('default')) {
                $this->error('User already has a trial or subscription.');
                return 1;
            }

            $subscriptionService->startTrial($user);
            $this->info("Trial started for user: {$user->email}");

        } else {
            // Start trials for all eligible users
            $users = User::whereDoesntHave('subscriptions')
                ->where('created_at', '>=', now()->subDays(30))
                ->get();

            $count = 0;
            foreach ($users as $user) {
                if (!$subscriptionService->isOnTrial($user)) {
                    $subscriptionService->startTrial($user);
                    $count++;
                }
            }

            $this->info("Started trials for {$count} users.");
        }

        return 0;
    }
}
