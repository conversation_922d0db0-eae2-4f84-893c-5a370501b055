<?php

namespace App\Services;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Models\User;
use Carbon\Carbon;
use Laravel\Cashier\Subscription;

class SubscriptionService
{
    public function getCurrentPlan(User $user): SubscriptionPlan
    {
        // Check if user is on trial
        if ($this->isOnTrial($user)) {
            return SubscriptionPlan::TRIAL;
        }

        // Check active subscription
        $subscription = $user->subscription('default');
        
        if (!$subscription || !$subscription->active()) {
            // No active subscription, return starter as default
            return SubscriptionPlan::STARTER;
        }

        // Determine plan from Stripe price ID
        $priceId = $subscription->stripe_price;
        
        foreach (SubscriptionPlan::getAvailablePlans() as $plan) {
            if ($priceId === $plan->stripePriceId(false) || $priceId === $plan->stripePriceId(true)) {
                return $plan;
            }
        }

        return SubscriptionPlan::STARTER;
    }

    public function getSubscriptionStatus(User $user): SubscriptionStatus
    {
        if ($this->isOnTrial($user)) {
            return SubscriptionStatus::TRIAL;
        }

        $subscription = $user->subscription('default');
        
        if (!$subscription) {
            return SubscriptionStatus::CANCELED;
        }

        return SubscriptionStatus::from($subscription->stripe_status);
    }

    public function isOnTrial(User $user): bool
    {
        return $user->onTrial('default') || $this->isInTrialPeriod($user);
    }

    public function isInTrialPeriod(User $user): bool
    {
        // Check if user was created within the last 30 days and has no subscription
        $trialEndDate = $user->created_at->addDays(30);
        $hasNoSubscription = !$user->subscription('default');
        
        return $hasNoSubscription && Carbon::now()->lessThan($trialEndDate);
    }

    public function getTrialDaysRemaining(User $user): int
    {
        if (!$this->isOnTrial($user)) {
            return 0;
        }

        if ($user->onTrial('default')) {
            return $user->trialEndsAt('default')->diffInDays(Carbon::now());
        }

        // For users in trial period based on creation date
        $trialEndDate = $user->created_at->addDays(30);
        return max(0, $trialEndDate->diffInDays(Carbon::now()));
    }

    public function canAccessFeature(User $user, string $feature): bool
    {
        $plan = $this->getCurrentPlan($user);
        $status = $this->getSubscriptionStatus($user);

        // Block access if subscription is not active
        if (!$status->isActive()) {
            return false;
        }

        return match ($feature) {
            'advanced_reporting' => $plan->hasAdvancedReporting(),
            'recurring_invoices' => $plan->hasRecurringInvoices(),
            'multi_currency' => $plan->hasMultiCurrency(),
            'budget_management' => $plan->hasBudgetManagement(),
            'custom_branding' => $plan->hasCustomBranding(),
            'data_export' => $plan->hasDataExport(),
            default => true, // Basic features are available to all plans
        };
    }

    public function hasReachedLimit(User $user, string $limitType, int $currentCount = null): bool
    {
        $plan = $this->getCurrentPlan($user);
        $status = $this->getSubscriptionStatus($user);

        // Allow everything during trial or if subscription is active
        if (!$status->isActive()) {
            return true; // Block everything if not active
        }

        if ($currentCount === null) {
            $currentCount = $this->getCurrentCount($user, $limitType);
        }

        return match ($limitType) {
            'companies' => $currentCount >= $plan->maxCompanies(),
            'users_per_company' => $currentCount >= $plan->maxUsersPerCompany(),
            'monthly_transactions' => $currentCount >= $plan->maxMonthlyTransactions(),
            'storage_gb' => $currentCount >= $plan->maxStorageGB(),
            'bank_accounts' => $currentCount >= $plan->maxBankAccounts(),
            default => false,
        };
    }

    private function getCurrentCount(User $user, string $limitType): int
    {
        return match ($limitType) {
            'companies' => $user->allCompanies()->count(),
            'users_per_company' => $user->currentCompany?->allUsers()->count() ?? 0,
            'monthly_transactions' => $this->getMonthlyTransactionCount($user),
            'storage_gb' => $this->getStorageUsageGB($user),
            'bank_accounts' => $user->currentCompany?->bankAccounts()->count() ?? 0,
            default => 0,
        };
    }

    private function getMonthlyTransactionCount(User $user): int
    {
        if (!$user->currentCompany) {
            return 0;
        }

        return $user->currentCompany
            ->transactions()
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }

    private function getStorageUsageGB(User $user): int
    {
        // This would need to be implemented based on your file storage system
        // For now, return 0 as placeholder
        return 0;
    }

    public function startTrial(User $user, SubscriptionPlan $plan = SubscriptionPlan::PROFESSIONAL): void
    {
        if (!$this->isOnTrial($user) && !$user->subscription('default')) {
            $user->newSubscription('default', $plan->stripePriceId())
                ->trialDays(30)
                ->create();
        }
    }

    public function upgradePlan(User $user, SubscriptionPlan $newPlan, bool $yearly = false): void
    {
        $subscription = $user->subscription('default');
        
        if ($subscription) {
            $subscription->swap($newPlan->stripePriceId($yearly));
        } else {
            $user->newSubscription('default', $newPlan->stripePriceId($yearly))->create();
        }
    }

    public function cancelSubscription(User $user, bool $immediately = false): void
    {
        $subscription = $user->subscription('default');
        
        if ($subscription) {
            if ($immediately) {
                $subscription->cancelNow();
            } else {
                $subscription->cancel();
            }
        }
    }
}
