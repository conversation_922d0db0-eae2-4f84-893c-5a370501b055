<?php

namespace App\Enums;

enum SubscriptionStatus: string
{
    case TRIAL = 'trial';
    case ACTIVE = 'active';
    case PAST_DUE = 'past_due';
    case CANCELED = 'canceled';
    case UNPAID = 'unpaid';
    case INCOMPLETE = 'incomplete';
    case INCOMPLETE_EXPIRED = 'incomplete_expired';
    case PAUSED = 'paused';

    public function label(): string
    {
        return match ($this) {
            self::TRIAL => 'Trial',
            self::ACTIVE => 'Active',
            self::PAST_DUE => 'Past Due',
            self::CANCELED => 'Canceled',
            self::UNPAID => 'Unpaid',
            self::INCOMPLETE => 'Incomplete',
            self::INCOMPLETE_EXPIRED => 'Incomplete Expired',
            self::PAUSED => 'Paused',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::TRIAL => 'info',
            self::ACTIVE => 'success',
            self::PAST_DUE => 'warning',
            self::CANCELED => 'danger',
            self::UNPAID => 'danger',
            self::INCOMPLETE => 'warning',
            self::INCOMPLETE_EXPIRED => 'danger',
            self::PAUSED => 'gray',
        };
    }

    public function isActive(): bool
    {
        return in_array($this, [self::TRIAL, self::ACTIVE]);
    }

    public function requiresPayment(): bool
    {
        return in_array($this, [self::PAST_DUE, self::UNPAID, self::INCOMPLETE]);
    }

    public function isExpired(): bool
    {
        return in_array($this, [self::CANCELED, self::INCOMPLETE_EXPIRED]);
    }
}
