<?php

namespace App\Enums;

enum SubscriptionPlan: string
{
    case TRIAL = 'trial';
    case STARTER = 'starter';
    case PROFESSIONAL = 'professional';

    public function label(): string
    {
        return match ($this) {
            self::TRIAL => 'Trial',
            self::STARTER => 'Starter',
            self::PROFESSIONAL => 'Professional',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::TRIAL => '30-day free trial with Professional features',
            self::STARTER => 'Perfect for small businesses getting started',
            self::PROFESSIONAL => 'Advanced features for growing businesses',
        };
    }

    public function monthlyPrice(): int
    {
        return match ($this) {
            self::TRIAL => 0,
            self::STARTER => 2900, // $29.00 in cents
            self::PROFESSIONAL => 7900, // $79.00 in cents
        };
    }

    public function yearlyPrice(): int
    {
        return match ($this) {
            self::TRIAL => 0,
            self::STARTER => 29000, // $290.00 in cents (10 months price)
            self::PROFESSIONAL => 79000, // $790.00 in cents (10 months price)
        };
    }

    public function stripePriceId(bool $yearly = false): ?string
    {
        // These will be set up in Stripe dashboard
        return match ($this) {
            self::TRIAL => null,
            self::STARTER => $yearly ? env('STRIPE_STARTER_YEARLY_PRICE_ID') : env('STRIPE_STARTER_MONTHLY_PRICE_ID'),
            self::PROFESSIONAL => $yearly ? env('STRIPE_PROFESSIONAL_YEARLY_PRICE_ID') : env('STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID'),
        };
    }

    public function maxCompanies(): int
    {
        return match ($this) {
            self::TRIAL => 3, // Same as Professional during trial
            self::STARTER => 1,
            self::PROFESSIONAL => 3,
        };
    }

    public function maxUsersPerCompany(): int
    {
        return match ($this) {
            self::TRIAL => 25, // Same as Professional during trial
            self::STARTER => 5,
            self::PROFESSIONAL => 25,
        };
    }

    public function maxMonthlyTransactions(): int
    {
        return match ($this) {
            self::TRIAL => 5000, // Same as Professional during trial
            self::STARTER => 500,
            self::PROFESSIONAL => 5000,
        };
    }

    public function maxStorageGB(): int
    {
        return match ($this) {
            self::TRIAL => 10, // Same as Professional during trial
            self::STARTER => 1,
            self::PROFESSIONAL => 10,
        };
    }

    public function maxBankAccounts(): int
    {
        return match ($this) {
            self::TRIAL => 999, // Unlimited during trial
            self::STARTER => 2,
            self::PROFESSIONAL => 999, // Unlimited
        };
    }

    public function hasAdvancedReporting(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function hasRecurringInvoices(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function hasMultiCurrency(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function hasBudgetManagement(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function hasCustomBranding(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function hasDataExport(): bool
    {
        return match ($this) {
            self::TRIAL => true,
            self::STARTER => false,
            self::PROFESSIONAL => true,
        };
    }

    public function supportLevel(): string
    {
        return match ($this) {
            self::TRIAL => 'Email support',
            self::STARTER => 'Email support',
            self::PROFESSIONAL => 'Priority email support',
        };
    }

    public static function getAvailablePlans(): array
    {
        return [
            self::STARTER,
            self::PROFESSIONAL,
        ];
    }
}
