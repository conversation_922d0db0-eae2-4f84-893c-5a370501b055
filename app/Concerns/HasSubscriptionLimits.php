<?php

namespace App\Concerns;

use App\Services\SubscriptionService;
use Illuminate\Database\Eloquent\Builder;

trait HasSubscriptionLimits
{
    public function scopeWithinSubscriptionLimits(Builder $query, string $limitType): Builder
    {
        $user = auth()->user();
        
        if (!$user) {
            return $query;
        }

        $subscriptionService = app(SubscriptionService::class);
        $plan = $subscriptionService->getCurrentPlan($user);

        return match ($limitType) {
            'companies' => $query->limit($plan->maxCompanies()),
            'users_per_company' => $query->limit($plan->maxUsersPerCompany()),
            'monthly_transactions' => $query->limit($plan->maxMonthlyTransactions()),
            'bank_accounts' => $query->limit($plan->maxBankAccounts()),
            default => $query,
        };
    }

    public function canCreateWithinLimits(string $limitType): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        $subscriptionService = app(SubscriptionService::class);
        
        return !$subscriptionService->hasReachedLimit($user, $limitType);
    }
}
