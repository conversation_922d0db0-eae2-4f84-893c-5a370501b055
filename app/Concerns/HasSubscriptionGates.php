<?php

namespace App\Concerns;

use App\Services\SubscriptionService;
use Filament\Notifications\Notification;

trait HasSubscriptionGates
{
    protected function checkSubscriptionLimits(string $limitType): bool
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        if ($subscriptionService->hasReachedLimit($user, $limitType)) {
            $plan = $subscriptionService->getCurrentPlan($user);
            
            Notification::make()
                ->title('Plan Limit Reached')
                ->body("You've reached the {$limitType} limit for your {$plan->label()} plan. Please upgrade to continue.")
                ->warning()
                ->actions([
                    \Filament\Notifications\Actions\Action::make('upgrade')
                        ->label('Upgrade Plan')
                        ->url(route('filament.company.pages.billing'))
                        ->button(),
                ])
                ->send();
                
            return false;
        }
        
        return true;
    }
    
    protected function checkFeatureAccess(string $feature): bool
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        if (!$subscriptionService->canAccessFeature($user, $feature)) {
            $plan = $subscriptionService->getCurrentPlan($user);
            
            Notification::make()
                ->title('Feature Not Available')
                ->body("This feature is not available in your {$plan->label()} plan. Please upgrade to access it.")
                ->warning()
                ->actions([
                    \Filament\Notifications\Actions\Action::make('upgrade')
                        ->label('Upgrade Plan')
                        ->url(route('filament.company.pages.billing'))
                        ->button(),
                ])
                ->send();
                
            return false;
        }
        
        return true;
    }
    
    protected function getSubscriptionStatus(): array
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        return [
            'plan' => $subscriptionService->getCurrentPlan($user),
            'status' => $subscriptionService->getSubscriptionStatus($user),
            'is_trial' => $subscriptionService->isOnTrial($user),
            'trial_days_remaining' => $subscriptionService->getTrialDaysRemaining($user),
        ];
    }
}
