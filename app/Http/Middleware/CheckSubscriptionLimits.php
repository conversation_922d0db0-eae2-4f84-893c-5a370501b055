<?php

namespace App\Http\Middleware;

use App\Services\SubscriptionService;
use Closure;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionLimits
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    public function handle(Request $request, Closure $next, string $limitType = null): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return $next($request);
        }

        $status = $this->subscriptionService->getSubscriptionStatus($user);

        // If subscription is not active, redirect to billing
        if (!$status->isActive()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Subscription required to access this feature.',
                    'redirect' => route('filament.company.pages.billing')
                ], 402);
            }

            Notification::make()
                ->title('Subscription Required')
                ->body('Please update your subscription to continue using this feature.')
                ->warning()
                ->send();

            return redirect()->route('filament.company.pages.billing');
        }

        // Check specific limits if provided
        if ($limitType && $this->subscriptionService->hasReachedLimit($user, $limitType)) {
            $plan = $this->subscriptionService->getCurrentPlan($user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => "You've reached the {$limitType} limit for your {$plan->label()} plan.",
                    'redirect' => route('filament.company.pages.billing')
                ], 402);
            }

            Notification::make()
                ->title('Plan Limit Reached')
                ->body("You've reached the {$limitType} limit for your {$plan->label()} plan. Please upgrade to continue.")
                ->warning()
                ->send();

            return redirect()->route('filament.company.pages.billing');
        }

        return $next($request);
    }
}
