<?php

namespace App\Http\Middleware;

use App\Services\SubscriptionService;
use Closure;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFeatureAccess
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    public function handle(Request $request, Closure $next, string $feature): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return $next($request);
        }

        if (!$this->subscriptionService->canAccessFeature($user, $feature)) {
            $plan = $this->subscriptionService->getCurrentPlan($user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => "This feature is not available in your {$plan->label()} plan.",
                    'redirect' => route('filament.company.pages.billing')
                ], 403);
            }

            Notification::make()
                ->title('Feature Not Available')
                ->body("This feature is not available in your {$plan->label()} plan. Please upgrade to access it.")
                ->warning()
                ->send();

            return redirect()->route('filament.company.pages.billing');
        }

        return $next($request);
    }
}
