<?php

namespace App\Http\Responses;

use App\Services\SubscriptionService;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class RegisterResponse implements RegistrationResponse
{
    public function toResponse($request): RedirectResponse | Redirector
    {
        $user = auth()->user();

        // Start trial for new user
        $subscriptionService = app(SubscriptionService::class);
        if (!$subscriptionService->isOnTrial($user) && !$user->subscription('default')) {
            $subscriptionService->startTrial($user);
        }

        // If user has no companies, redirect to welcome page
        if (!$user->currentCompany && $user->allCompanies()->count() === 0) {
            return redirect()->route('filament.company.pages.welcome');
        }

        // If user has a company, redirect to dashboard
        if ($user->currentCompany) {
            return redirect()->route('filament.company.pages.dashboard', ['tenant' => $user->currentCompany]);
        }

        // Fallback to welcome page
        return redirect()->route('filament.company.pages.welcome');
    }
}
