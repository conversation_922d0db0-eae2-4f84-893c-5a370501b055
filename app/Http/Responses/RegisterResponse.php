<?php

namespace App\Http\Responses;

use App\Services\SubscriptionService;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class RegisterResponse implements RegistrationResponse
{
    public function toResponse($request): RedirectResponse | Redirector
    {
        $user = auth()->user();

        // Start trial for new user
        $subscriptionService = app(SubscriptionService::class);
        if (!$subscriptionService->isOnTrial($user) && !$user->subscription('default')) {
            $subscriptionService->startTrial($user);
        }

        // Always redirect to user-level welcome page for consistent onboarding
        return redirect()->route('filament.user.pages.welcome');
    }
}
