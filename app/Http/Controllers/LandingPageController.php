<?php

namespace App\Http\Controllers;

use App\Enums\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\View\View;

class LandingPageController extends Controller
{
    public function index(): View
    {
        return view('landing.index', [
            'plans' => $this->getPlans(),
            'features' => $this->getFeatures(),
        ]);
    }

    public function pricing(): View
    {
        return view('landing.pricing', [
            'plans' => $this->getPlans(),
        ]);
    }

    public function features(): View
    {
        return view('landing.features', [
            'features' => $this->getFeatures(),
        ]);
    }

    public function about(): View
    {
        return view('landing.about');
    }

    public function contact(): View
    {
        return view('landing.contact');
    }

    private function getPlans(): array
    {
        return [
            'starter' => [
                'name' => 'Starter',
                'price_monthly' => 29,
                'price_yearly' => 290,
                'description' => 'Perfect for small businesses getting started',
                'features' => [
                    '1 Company',
                    'Up to 5 Users',
                    '500 Transactions/month',
                    '1GB Storage',
                    'Up to 2 Bank Accounts',
                    'Basic Invoicing',
                    'Expense Tracking',
                    'Email Support',
                ],
                'cta' => 'Start Free Trial',
                'popular' => false,
            ],
            'professional' => [
                'name' => 'Professional',
                'price_monthly' => 79,
                'price_yearly' => 790,
                'description' => 'Advanced features for growing businesses',
                'features' => [
                    'Up to 3 Companies',
                    'Up to 25 Users',
                    '5,000 Transactions/month',
                    '10GB Storage',
                    'Unlimited Bank Accounts',
                    'Advanced Reporting',
                    'Recurring Invoices',
                    'Multi-Currency Support',
                    'Budget Management',
                    'Custom Branding',
                    'Data Export',
                    'Priority Support',
                ],
                'cta' => 'Start Free Trial',
                'popular' => true,
            ],
        ];
    }

    private function getFeatures(): array
    {
        return [
            [
                'icon' => 'heroicon-o-calculator',
                'title' => 'Double-Entry Accounting',
                'description' => 'Professional accounting with automatic journal entries and real-time balance tracking.',
            ],
            [
                'icon' => 'heroicon-o-document-text',
                'title' => 'Invoice Management',
                'description' => 'Create, send, and track invoices with automated payment reminders and online payments.',
            ],
            [
                'icon' => 'heroicon-o-chart-bar',
                'title' => 'Financial Reporting',
                'description' => 'Comprehensive reports including P&L, balance sheets, cash flow, and custom analytics.',
            ],
            [
                'icon' => 'heroicon-o-building-library',
                'title' => 'Bank Integration',
                'description' => 'Connect your bank accounts for automatic transaction import and reconciliation.',
            ],
            [
                'icon' => 'heroicon-o-users',
                'title' => 'Multi-User Access',
                'description' => 'Collaborate with your team with role-based permissions and access controls.',
            ],
            [
                'icon' => 'heroicon-o-globe-alt',
                'title' => 'Multi-Currency',
                'description' => 'Handle international business with support for multiple currencies and exchange rates.',
            ],
            [
                'icon' => 'heroicon-o-arrow-path',
                'title' => 'Recurring Billing',
                'description' => 'Automate recurring invoices and subscription billing for predictable revenue.',
            ],
            [
                'icon' => 'heroicon-o-shield-check',
                'title' => 'Secure & Compliant',
                'description' => 'Bank-level security with data encryption and compliance with accounting standards.',
            ],
        ];
    }
}
