<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierController;
use Symfony\Component\HttpFoundation\Response;

class StripeWebhookController extends CashierController
{
    /**
     * Handle customer subscription created.
     */
    public function handleCustomerSubscriptionCreated(array $payload): Response
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        
        if ($user) {
            // Log subscription creation
            logger('Subscription created for user: ' . $user->email);
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription updated.
     */
    public function handleCustomerSubscriptionUpdated(array $payload): Response
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        
        if ($user) {
            // Log subscription update
            logger('Subscription updated for user: ' . $user->email);
            
            // You can add custom logic here for subscription changes
            $this->handleSubscriptionChange($user, $payload['data']['object']);
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription deleted.
     */
    public function handleCustomerSubscriptionDeleted(array $payload): Response
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        
        if ($user) {
            // Log subscription cancellation
            logger('Subscription cancelled for user: ' . $user->email);
            
            // You can add custom logic here for subscription cancellation
            $this->handleSubscriptionCancellation($user, $payload['data']['object']);
        }

        return $this->successMethod();
    }

    /**
     * Handle invoice payment succeeded.
     */
    public function handleInvoicePaymentSucceeded(array $payload): Response
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        
        if ($user) {
            // Log successful payment
            logger('Payment succeeded for user: ' . $user->email);
            
            // You can add custom logic here for successful payments
            $this->handleSuccessfulPayment($user, $payload['data']['object']);
        }

        return $this->successMethod();
    }

    /**
     * Handle invoice payment failed.
     */
    public function handleInvoicePaymentFailed(array $payload): Response
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        
        if ($user) {
            // Log failed payment
            logger('Payment failed for user: ' . $user->email);
            
            // You can add custom logic here for failed payments
            $this->handleFailedPayment($user, $payload['data']['object']);
        }

        return $this->successMethod();
    }

    /**
     * Get user by Stripe customer ID.
     */
    protected function getUserByStripeId($stripeId)
    {
        return User::where('stripe_id', $stripeId)->first();
    }

    /**
     * Handle subscription change logic.
     */
    protected function handleSubscriptionChange(User $user, array $subscription): void
    {
        // Add any custom logic for subscription changes
        // For example, updating user permissions, sending notifications, etc.
    }

    /**
     * Handle subscription cancellation logic.
     */
    protected function handleSubscriptionCancellation(User $user, array $subscription): void
    {
        // Add any custom logic for subscription cancellation
        // For example, scheduling data retention, sending notifications, etc.
    }

    /**
     * Handle successful payment logic.
     */
    protected function handleSuccessfulPayment(User $user, array $invoice): void
    {
        // Add any custom logic for successful payments
        // For example, sending thank you emails, updating credits, etc.
    }

    /**
     * Handle failed payment logic.
     */
    protected function handleFailedPayment(User $user, array $invoice): void
    {
        // Add any custom logic for failed payments
        // For example, sending dunning emails, updating account status, etc.
    }
}
