<?php

namespace App\Http\Controllers;

use App\Enums\SubscriptionPlan;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class SubscriptionController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Create a checkout session for subscription.
     */
    public function checkout(Request $request): JsonResponse
    {
        $request->validate([
            'plan' => 'required|string|in:starter,professional',
            'yearly' => 'boolean',
        ]);

        $user = $request->user();
        $plan = SubscriptionPlan::from($request->plan);
        $yearly = $request->boolean('yearly', false);

        try {
            // Create or get Stripe customer
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer();
            }

            // Create checkout session
            $checkout = $user->newSubscription('default', $plan->stripePriceId($yearly))
                ->checkout([
                    'success_url' => route('filament.company.pages.billing') . '?success=true',
                    'cancel_url' => route('filament.company.pages.billing') . '?cancelled=true',
                ]);

            return response()->json([
                'checkout_url' => $checkout->url,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to create checkout session: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update existing subscription.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'plan' => 'required|string|in:starter,professional',
            'yearly' => 'boolean',
        ]);

        $user = $request->user();
        $plan = SubscriptionPlan::from($request->plan);
        $yearly = $request->boolean('yearly', false);

        try {
            $this->subscriptionService->upgradePlan($user, $plan, $yearly);

            return redirect()
                ->route('filament.company.pages.billing')
                ->with('success', 'Subscription updated successfully!');

        } catch (\Exception $e) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'Failed to update subscription: ' . $e->getMessage());
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Request $request): RedirectResponse
    {
        $user = $request->user();
        $immediately = $request->boolean('immediately', false);

        try {
            $this->subscriptionService->cancelSubscription($user, $immediately);

            $message = $immediately 
                ? 'Subscription cancelled immediately.' 
                : 'Subscription will be cancelled at the end of the current billing period.';

            return redirect()
                ->route('filament.company.pages.billing')
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'Failed to cancel subscription: ' . $e->getMessage());
        }
    }

    /**
     * Resume cancelled subscription.
     */
    public function resume(Request $request): RedirectResponse
    {
        $user = $request->user();
        $subscription = $user->subscription('default');

        if (!$subscription || !$subscription->cancelled()) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'No cancelled subscription found.');
        }

        try {
            $subscription->resume();

            return redirect()
                ->route('filament.company.pages.billing')
                ->with('success', 'Subscription resumed successfully!');

        } catch (\Exception $e) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'Failed to resume subscription: ' . $e->getMessage());
        }
    }

    /**
     * Get billing portal URL.
     */
    public function billingPortal(Request $request): RedirectResponse
    {
        $user = $request->user();

        if (!$user->hasStripeId()) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'No billing information found.');
        }

        try {
            $url = $user->billingPortalUrl(route('filament.company.pages.billing'));
            
            return redirect($url);

        } catch (\Exception $e) {
            return redirect()
                ->route('filament.company.pages.billing')
                ->with('error', 'Failed to access billing portal: ' . $e->getMessage());
        }
    }
}
