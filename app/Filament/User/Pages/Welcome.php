<?php

namespace App\Filament\User\Pages;

use App\Services\SubscriptionService;
use Filament\Actions\Action;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class Welcome extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-sparkles';
    
    protected static string $view = 'filament.user.pages.welcome';
    
    protected static bool $shouldRegisterNavigation = true;
    
    protected static ?string $navigationLabel = 'Welcome';
    
    protected static ?int $navigationSort = -1;
    
    public function getTitle(): string | Htmlable
    {
        return 'Welcome to ERPSaaS!';
    }
    
    public function getHeading(): string | Htmlable
    {
        return 'Welcome to Your Free Trial!';
    }
    
    public function getSubheading(): string | Htmlable | null
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        $daysRemaining = $subscriptionService->getTrialDaysRemaining($user);
        
        return "You have {$daysRemaining} days of free access to all Professional features.";
    }
    
    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_company')
                ->label('Create New Company')
                ->icon('heroicon-o-building-office')
                ->color('primary')
                ->size('lg')
                ->url(route('filament.company.tenant.registration'))
                ->visible(fn () => auth()->user()->allCompanies()->count() < 3), // Limit based on plan
                
            Action::make('manage_subscription')
                ->label('Manage Subscription')
                ->icon('heroicon-o-credit-card')
                ->color('gray')
                ->url(fn () => auth()->user()->currentCompany 
                    ? route('filament.company.pages.billing', ['tenant' => auth()->user()->currentCompany])
                    : '#'),
        ];
    }
    
    public function mount(): void
    {
        $user = auth()->user();
        
        // Start trial if not already started
        $subscriptionService = app(SubscriptionService::class);
        if (!$subscriptionService->isOnTrial($user) && !$user->subscription('default')) {
            $subscriptionService->startTrial($user);
        }
    }
    
    public function getViewData(): array
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        return [
            'user' => $user,
            'companies' => $user->allCompanies(),
            'trialDaysRemaining' => $subscriptionService->getTrialDaysRemaining($user),
            'currentPlan' => $subscriptionService->getCurrentPlan($user),
            'canCreateMoreCompanies' => $user->allCompanies()->count() < 3, // Based on plan limits
        ];
    }
}
