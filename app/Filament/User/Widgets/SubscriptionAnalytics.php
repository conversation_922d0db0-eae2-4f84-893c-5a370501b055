<?php

namespace App\Filament\User\Widgets;

use App\Enums\SubscriptionPlan;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Lara<PERSON>\Cashier\Subscription;

class SubscriptionAnalytics extends BaseWidget
{
    protected function getStats(): array
    {
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('stripe_status', 'active')->count();
        $trialSubscriptions = Subscription::where('stripe_status', 'trialing')->count();
        $cancelledSubscriptions = Subscription::where('stripe_status', 'canceled')->count();
        
        $totalUsers = User::count();
        $usersWithSubscriptions = User::whereHas('subscriptions')->count();
        $usersWithoutSubscriptions = $totalUsers - $usersWithSubscriptions;
        
        // Calculate MRR (Monthly Recurring Revenue) - simplified
        $starterMRR = Subscription::where('stripe_status', 'active')
            ->where('name', 'default')
            ->whereHas('user', function ($query) {
                // This is a simplified approach - in reality you'd check the price ID
            })
            ->count() * (SubscriptionPlan::STARTER->monthlyPrice() / 100);
            
        $professionalMRR = Subscription::where('stripe_status', 'active')
            ->where('name', 'default')
            ->count() * (SubscriptionPlan::PROFESSIONAL->monthlyPrice() / 100);
            
        $totalMRR = $starterMRR + $professionalMRR;
        
        return [
            Stat::make('Total Subscriptions', $totalSubscriptions)
                ->description('All subscriptions')
                ->descriptionIcon('heroicon-m-credit-card')
                ->color('primary'),
                
            Stat::make('Active Subscriptions', $activeSubscriptions)
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
                
            Stat::make('Trial Subscriptions', $trialSubscriptions)
                ->description('In trial period')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),
                
            Stat::make('Cancelled Subscriptions', $cancelledSubscriptions)
                ->description('Cancelled subscriptions')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
                
            Stat::make('Users Without Subscription', $usersWithoutSubscriptions)
                ->description('Potential customers')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('warning'),
                
            Stat::make('Monthly Recurring Revenue', '$' . number_format($totalMRR, 2))
                ->description('Estimated MRR')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
        ];
    }
}
