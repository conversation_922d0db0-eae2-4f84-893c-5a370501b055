<?php

namespace App\Filament\User\Resources;

use App\Filament\User\Resources\UserResource\Pages;
use App\Models\User;
use App\Services\SubscriptionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    
    protected static ?string $navigationLabel = 'Users';
    
    protected static ?string $modelLabel = 'User';
    
    protected static ?string $pluralModelLabel = 'Users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                            
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                            
                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('Email Verified At'),
                            
                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('Created At')
                            ->disabled(),
                    ]),
                    
                Forms\Components\Section::make('Subscription Information')
                    ->schema([
                        Forms\Components\Placeholder::make('subscription_status')
                            ->label('Current Plan')
                            ->content(function (User $record): string {
                                $subscriptionService = app(SubscriptionService::class);
                                $plan = $subscriptionService->getCurrentPlan($record);
                                $status = $subscriptionService->getSubscriptionStatus($record);
                                
                                return "{$plan->label()} ({$status->label()})";
                            }),
                            
                        Forms\Components\Placeholder::make('trial_info')
                            ->label('Trial Information')
                            ->content(function (User $record): string {
                                $subscriptionService = app(SubscriptionService::class);
                                
                                if ($subscriptionService->isOnTrial($record)) {
                                    $daysRemaining = $subscriptionService->getTrialDaysRemaining($record);
                                    return "Trial active - {$daysRemaining} days remaining";
                                }
                                
                                return 'Not on trial';
                            }),
                            
                        Forms\Components\Placeholder::make('stripe_id')
                            ->label('Stripe Customer ID')
                            ->content(fn (User $record): string => $record->stripe_id ?? 'Not created'),
                    ])
                    ->visible(fn (?User $record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('subscription_plan')
                    ->label('Plan')
                    ->getStateUsing(function (User $record): string {
                        $subscriptionService = app(SubscriptionService::class);
                        return $subscriptionService->getCurrentPlan($record)->label();
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Trial' => 'info',
                        'Starter' => 'success',
                        'Professional' => 'warning',
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('subscription_status')
                    ->label('Status')
                    ->getStateUsing(function (User $record): string {
                        $subscriptionService = app(SubscriptionService::class);
                        return $subscriptionService->getSubscriptionStatus($record)->label();
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Active' => 'success',
                        'Trial' => 'info',
                        'Past Due' => 'warning',
                        'Canceled' => 'danger',
                        'Unpaid' => 'danger',
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('companies_count')
                    ->label('Companies')
                    ->counts('allCompanies')
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('email_verified_at')
                    ->label('Verified')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subscription_plan')
                    ->label('Plan')
                    ->options([
                        'trial' => 'Trial',
                        'starter' => 'Starter',
                        'professional' => 'Professional',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        // This is a simplified filter - in a real implementation,
                        // you'd need to join with subscriptions table
                        return $query;
                    }),
                    
                Tables\Filters\Filter::make('has_subscription')
                    ->label('Has Active Subscription')
                    ->query(fn (Builder $query): Builder => $query->whereHas('subscriptions', function (Builder $query) {
                        $query->where('stripe_status', 'active');
                    })),
                    
                Tables\Filters\Filter::make('on_trial')
                    ->label('On Trial')
                    ->query(fn (Builder $query): Builder => $query->whereHas('subscriptions', function (Builder $query) {
                        $query->where('stripe_status', 'trialing');
                    })),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Tables\Actions\Action::make('start_trial')
                    ->label('Start Trial')
                    ->icon('heroicon-o-play')
                    ->color('info')
                    ->action(function (User $record) {
                        $subscriptionService = app(SubscriptionService::class);
                        $subscriptionService->startTrial($record);
                    })
                    ->requiresConfirmation()
                    ->visible(function (User $record) {
                        $subscriptionService = app(SubscriptionService::class);
                        return !$subscriptionService->isOnTrial($record) && !$record->subscription('default');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
