<?php

namespace App\Filament\Company\Widgets;

use App\Enums\SubscriptionStatus;
use App\Services\SubscriptionService;
use Filament\Widgets\Widget;

class SubscriptionAlert extends Widget
{
    protected static string $view = 'filament.company.widgets.subscription-alert';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = -10; // Show at the top

    public function getViewData(): array
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        $status = $subscriptionService->getSubscriptionStatus($user);
        $plan = $subscriptionService->getCurrentPlan($user);
        $isOnTrial = $subscriptionService->isOnTrial($user);
        $trialDaysRemaining = $subscriptionService->getTrialDaysRemaining($user);
        
        return [
            'status' => $status,
            'plan' => $plan,
            'isOnTrial' => $isOnTrial,
            'trialDaysRemaining' => $trialDaysRemaining,
            'showAlert' => $this->shouldShowAlert($status, $isOnTrial, $trialDaysRemaining),
        ];
    }
    
    private function shouldShowAlert(SubscriptionStatus $status, bool $isOnTrial, int $trialDaysRemaining): bool
    {
        // Show alert if:
        // - On trial with less than 7 days remaining
        // - Subscription requires payment
        // - Subscription is expired
        return ($isOnTrial && $trialDaysRemaining <= 7) || 
               $status->requiresPayment() || 
               $status->isExpired();
    }
}
