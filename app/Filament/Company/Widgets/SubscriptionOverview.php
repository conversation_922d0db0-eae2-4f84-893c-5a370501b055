<?php

namespace App\Filament\Company\Widgets;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Services\SubscriptionService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SubscriptionOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        $plan = $subscriptionService->getCurrentPlan($user);
        $status = $subscriptionService->getSubscriptionStatus($user);
        
        $stats = [
            Stat::make('Current Plan', $plan->label())
                ->description($plan->description())
                ->color($this->getPlanColor($plan)),
        ];

        if ($status === SubscriptionStatus::TRIAL) {
            $daysRemaining = $subscriptionService->getTrialDaysRemaining($user);
            $stats[] = Stat::make('Trial Days Remaining', $daysRemaining)
                ->description('Days left in your free trial')
                ->color($daysRemaining > 7 ? 'success' : 'warning');
        } else {
            $stats[] = Stat::make('Status', $status->label())
                ->description('Subscription status')
                ->color($status->color());
        }

        // Usage stats
        $stats[] = $this->getUsageStat($user, $subscriptionService, $plan);

        return $stats;
    }

    private function getPlanColor(SubscriptionPlan $plan): string
    {
        return match ($plan) {
            SubscriptionPlan::TRIAL => 'info',
            SubscriptionPlan::STARTER => 'success',
            SubscriptionPlan::PROFESSIONAL => 'warning',
        };
    }

    private function getUsageStat($user, SubscriptionService $subscriptionService, SubscriptionPlan $plan): Stat
    {
        $currentCompanies = $user->allCompanies()->count();
        $maxCompanies = $plan->maxCompanies();
        
        return Stat::make('Companies Used', "{$currentCompanies} / {$maxCompanies}")
            ->description('Companies in your account')
            ->color($currentCompanies >= $maxCompanies ? 'danger' : 'success');
    }
}
