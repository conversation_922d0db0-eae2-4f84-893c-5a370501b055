<?php

namespace App\Filament\Company\Pages;

use App\Services\SubscriptionService;
use Filament\Actions\Action;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class Welcome extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-sparkles';

    protected static string $view = 'filament.company.pages.welcome';

    protected static bool $shouldRegisterNavigation = true;

    protected static ?string $navigationLabel = 'Welcome';

    protected static ?int $navigationSort = -1; // Show at the top
    
    public function getTitle(): string | Htmlable
    {
        return 'Welcome to ERPSaaS!';
    }
    
    public function getHeading(): string | Htmlable
    {
        return 'Welcome to Your Free Trial!';
    }
    
    public function getSubheading(): string | Htmlable | null
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        $daysRemaining = $subscriptionService->getTrialDaysRemaining($user);

        // Debug info
        $isOnTrial = $subscriptionService->isOnTrial($user);
        $hasSubscription = $user->subscription('default') ? 'Yes' : 'No';
        $onTrialDefault = $user->onTrial('default') ? 'Yes' : 'No';

        return "You have {$daysRemaining} days of free access to all Professional features. Let's get you started! (Trial: {$isOnTrial}, Sub: {$hasSubscription}, OnTrial: {$onTrialDefault})";
    }
    
    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_company')
                ->label('Create Your First Company')
                ->icon('heroicon-o-building-office')
                ->color('primary')
                ->size('lg')
                ->url(route('filament.company.tenant.registration'))
                ->visible(fn () => !auth()->user()->currentCompany),
                
            Action::make('go_to_dashboard')
                ->label('Go to Dashboard')
                ->icon('heroicon-o-home')
                ->color('primary')
                ->size('lg')
                ->url(fn () => route('filament.company.pages.dashboard', ['tenant' => \Filament\Facades\Filament::getTenant()]))
                ->visible(fn () => \Filament\Facades\Filament::getTenant()),
        ];
    }
    
    public function mount(): void
    {
        $user = auth()->user();

        // Start trial if not already started
        $subscriptionService = app(SubscriptionService::class);
        if (!$subscriptionService->isOnTrial($user) && !$user->subscription('default')) {
            $subscriptionService->startTrial($user);
        }
    }
    
    public function getViewData(): array
    {
        $user = auth()->user();
        $subscriptionService = app(SubscriptionService::class);
        
        return [
            'user' => $user,
            'trialDaysRemaining' => $subscriptionService->getTrialDaysRemaining($user),
            'currentPlan' => $subscriptionService->getCurrentPlan($user),
            'features' => [
                'Complete Accounting System',
                'Professional Invoicing',
                'Bank Account Integration',
                'Financial Reporting',
                'Multi-User Access',
                'Customer Management',
                'Vendor Management',
                'Expense Tracking',
            ],
        ];
    }
}
