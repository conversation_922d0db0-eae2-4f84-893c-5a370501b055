<?php

namespace App\Filament\Company\Pages;

use App\Enums\SubscriptionPlan;
use App\Services\SubscriptionService;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;

class Billing extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationLabel = 'Billing';
    protected static ?string $title = 'Billing & Subscription';
    protected static string $view = 'filament.user.pages.billing';
    protected static ?int $navigationSort = 100;
    protected static bool $shouldRegisterNavigation = false; // Disabled - moved to user panel

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Current Subscription')
                    ->description('Manage your subscription plan and billing')
                    ->schema([
                        Radio::make('plan')
                            ->label('Select Plan')
                            ->options([
                                SubscriptionPlan::STARTER->value => $this->getPlanOption(SubscriptionPlan::STARTER),
                                SubscriptionPlan::PROFESSIONAL->value => $this->getPlanOption(SubscriptionPlan::PROFESSIONAL),
                            ])
                            ->descriptions([
                                SubscriptionPlan::STARTER->value => $this->getPlanDescription(SubscriptionPlan::STARTER),
                                SubscriptionPlan::PROFESSIONAL->value => $this->getPlanDescription(SubscriptionPlan::PROFESSIONAL),
                            ])
                            ->default($this->getCurrentPlan()->value)
                            ->live(),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        $user = \Illuminate\Support\Facades\Auth::user();
        $subscription = $user->subscription('default');

        return [
            Action::make('start_subscription')
                ->label('Start Subscription')
                ->icon('heroicon-o-credit-card')
                ->action('startSubscription')
                ->visible(fn () => !$user->hasStripeId() && !$subscription),

            Action::make('manage_billing')
                ->label('Manage Billing')
                ->icon('heroicon-o-credit-card')
                ->url(fn () => $this->getBillingPortalUrl())
                ->openUrlInNewTab()
                ->visible(fn () => $user->hasStripeId()),

            Action::make('update_plan')
                ->label('Update Plan')
                ->icon('heroicon-o-arrow-up')
                ->action('updatePlan')
                ->requiresConfirmation()
                ->modalHeading('Update Subscription Plan')
                ->modalDescription('Are you sure you want to update your subscription plan?')
                ->visible(fn () => $this->canUpdatePlan()),

            Action::make('cancel_subscription')
                ->label('Cancel Subscription')
                ->icon('heroicon-o-x-mark')
                ->action('cancelSubscription')
                ->requiresConfirmation()
                ->modalHeading('Cancel Subscription')
                ->modalDescription('Are you sure you want to cancel your subscription? You will lose access to premium features.')
                ->color('danger')
                ->visible(fn () => $subscription && $subscription->active() && !$subscription->cancelled()),

            Action::make('resume_subscription')
                ->label('Resume Subscription')
                ->icon('heroicon-o-play')
                ->action('resumeSubscription')
                ->color('success')
                ->visible(fn () => $subscription && $subscription->cancelled()),
        ];
    }

    public function updatePlan(): void
    {
        $selectedPlan = SubscriptionPlan::from($this->data['plan']);
        $currentPlan = $this->getCurrentPlan();

        if ($selectedPlan === $currentPlan) {
            Notification::make()
                ->title('No Change Required')
                ->body('You are already on the selected plan.')
                ->info()
                ->send();
            return;
        }

        try {
            $subscriptionService = app(SubscriptionService::class);
            $subscriptionService->upgradePlan(\Illuminate\Support\Facades\Auth::user(), $selectedPlan);

            Notification::make()
                ->title('Plan Updated')
                ->body("Your subscription has been updated to {$selectedPlan->label()}.")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Update Failed')
                ->body('There was an error updating your subscription. Please try again.')
                ->danger()
                ->send();
        }
    }

    public function startSubscription(): void
    {
        $selectedPlan = SubscriptionPlan::from($this->data['plan'] ?? 'starter');

        try {
            $user = \Illuminate\Support\Facades\Auth::user();

            // Create Stripe customer if needed
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer();
            }

            // Redirect to Stripe checkout
            $checkout = $user->newSubscription('default', $selectedPlan->stripePriceId())
                ->checkout([
                    'success_url' => route('filament.company.pages.billing') . '?success=true',
                    'cancel_url' => route('filament.company.pages.billing') . '?cancelled=true',
                ]);

            $this->redirect($checkout->url);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Checkout Failed')
                ->body('There was an error creating the checkout session. Please try again.')
                ->danger()
                ->send();
        }
    }

    public function cancelSubscription(): void
    {
        try {
            $user = \Illuminate\Support\Facades\Auth::user();
            $subscription = $user->subscription('default');

            if ($subscription) {
                $subscription->cancel();

                Notification::make()
                    ->title('Subscription Cancelled')
                    ->body('Your subscription will be cancelled at the end of the current billing period.')
                    ->success()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Cancellation Failed')
                ->body('There was an error cancelling your subscription. Please try again.')
                ->danger()
                ->send();
        }
    }

    public function resumeSubscription(): void
    {
        try {
            $user = \Illuminate\Support\Facades\Auth::user();
            $subscription = $user->subscription('default');

            if ($subscription && $subscription->cancelled()) {
                $subscription->resume();

                Notification::make()
                    ->title('Subscription Resumed')
                    ->body('Your subscription has been resumed successfully.')
                    ->success()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Resume Failed')
                ->body('There was an error resuming your subscription. Please try again.')
                ->danger()
                ->send();
        }
    }

    private function getCurrentPlan(): SubscriptionPlan
    {
        return app(SubscriptionService::class)->getCurrentPlan(\Illuminate\Support\Facades\Auth::user());
    }

    private function canUpdatePlan(): bool
    {
        $user = \Illuminate\Support\Facades\Auth::user();
        return $user->hasStripeId() && isset($this->data['plan']) &&
               SubscriptionPlan::from($this->data['plan']) !== $this->getCurrentPlan();
    }

    private function getBillingPortalUrl(): string
    {
        return \Illuminate\Support\Facades\Auth::user()->billingPortalUrl(route('filament.company.pages.billing'));
    }

    private function getPlanOption(SubscriptionPlan $plan): string
    {
        $monthly = '$' . number_format($plan->monthlyPrice() / 100, 2);
        $yearly = '$' . number_format($plan->yearlyPrice() / 100, 2);
        
        return "{$plan->label()} - {$monthly}/month or {$yearly}/year";
    }

    private function getPlanDescription(SubscriptionPlan $plan): string
    {
        $features = [
            "Up to {$plan->maxCompanies()} " . ($plan->maxCompanies() === 1 ? 'company' : 'companies'),
            "Up to {$plan->maxUsersPerCompany()} users per company",
            "Up to {$plan->maxMonthlyTransactions()} transactions per month",
            "{$plan->maxStorageGB()}GB storage",
        ];

        if ($plan->maxBankAccounts() === 999) {
            $features[] = "Unlimited bank accounts";
        } else {
            $features[] = "Up to {$plan->maxBankAccounts()} bank accounts";
        }

        if ($plan->hasAdvancedReporting()) {
            $features[] = "Advanced reporting";
        }

        if ($plan->hasRecurringInvoices()) {
            $features[] = "Recurring invoices";
        }

        if ($plan->hasMultiCurrency()) {
            $features[] = "Multi-currency support";
        }

        if ($plan->hasBudgetManagement()) {
            $features[] = "Budget management";
        }

        if ($plan->hasCustomBranding()) {
            $features[] = "Custom branding";
        }

        if ($plan->hasDataExport()) {
            $features[] = "Data export";
        }

        return implode(' • ', $features);
    }
}
