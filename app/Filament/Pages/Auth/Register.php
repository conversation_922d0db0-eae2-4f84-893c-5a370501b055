<?php

namespace App\Filament\Pages\Auth;

use Filament\Actions\Action;
use Wallo\FilamentCompanies\Pages\Auth\Register as BaseRegister;
use Illuminate\Contracts\Support\Htmlable;

class Register extends BaseRegister
{
    public function getHeading(): string | Htmlable
    {
        return 'Start Your Free 30-Day Trial';
    }

    public function getSubheading(): string | Htmlable | null
    {
        return 'Get full access to all Professional features. No credit card required.';
    }

    public function getRegisterFormAction(): Action
    {
        return parent::getRegisterFormAction()
            ->label('Start Free Trial');
    }

    public function hasFullWidthFormActions(): bool
    {
        return true;
    }

    /**
     * @return array<Action>
     */
    protected function getFooterActions(): array
    {
        return [
            Action::make('back_to_landing')
                ->label('← Back to Homepage')
                ->url(route('landing.index'))
                ->color('gray')
                ->link(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        $user = auth()->user();

        // Start trial for new user
        $subscriptionService = app(\App\Services\SubscriptionService::class);
        if (!$subscriptionService->isOnTrial($user) && !$user->subscription('default')) {
            $subscriptionService->startTrial($user);
        }

        // Redirect to user-level welcome page
        return route('filament.user.pages.welcome');
    }
}
