<?php

use App\Http\Controllers\DocumentPrintController;
use App\Http\Middleware\AllowSameOriginFrame;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Route;

// Landing Page Routes (redirect authenticated users to app)
Route::middleware(['guest'])->group(function () {
    Route::get('/', [\App\Http\Controllers\LandingPageController::class, 'index'])->name('landing.index');
    Route::get('/pricing', [\App\Http\Controllers\LandingPageController::class, 'pricing'])->name('landing.pricing');
    Route::get('/features', [\App\Http\Controllers\LandingPageController::class, 'features'])->name('landing.features');
    Route::get('/about', [\App\Http\Controllers\LandingPageController::class, 'about'])->name('landing.about');
    Route::get('/contact', [\App\Http\Controllers\LandingPageController::class, 'contact'])->name('landing.contact');
});

// SEO Routes
Route::get('/sitemap.xml', function () {
    $urls = [
        ['url' => route('landing.index'), 'priority' => '1.0', 'changefreq' => 'weekly'],
        ['url' => route('landing.pricing'), 'priority' => '0.9', 'changefreq' => 'monthly'],
        ['url' => route('landing.features'), 'priority' => '0.8', 'changefreq' => 'monthly'],
        ['url' => route('landing.about'), 'priority' => '0.6', 'changefreq' => 'yearly'],
        ['url' => route('landing.contact'), 'priority' => '0.7', 'changefreq' => 'yearly'],
    ];

    return response()->view('sitemap', compact('urls'))
        ->header('Content-Type', 'application/xml');
})->name('sitemap');

Route::get('/robots.txt', function () {
    $content = "User-agent: *\n";
    $content .= "Allow: /\n";
    $content .= "Sitemap: " . route('sitemap') . "\n";

    return response($content)->header('Content-Type', 'text/plain');
})->name('robots');

// Redirect to app for authenticated users
Route::get('/app', function () {
    if (\Illuminate\Support\Facades\Auth::check()) {
        return redirect(Filament::getDefaultPanel()->getUrl());
    }
    return redirect()->route('filament.company.auth.login');
})->name('app.redirect');

Route::middleware(['auth'])->group(function () {
    Route::get('documents/{documentType}/{id}/print', [DocumentPrintController::class, 'show'])
        ->middleware(AllowSameOriginFrame::class)
        ->name('documents.print');

    // Company switching
    Route::post('current-company', function () {
        $companyId = request('company_id');
        $user = auth()->user();

        // Verify user owns this company
        if ($user->allCompanies()->where('id', $companyId)->exists()) {
            $user->switchCompany($user->allCompanies()->find($companyId));
        }

        return redirect()->back();
    })->name('current-company.update');

    // Subscription routes
    Route::prefix('subscription')->name('subscription.')->group(function () {
        Route::post('checkout', [\App\Http\Controllers\SubscriptionController::class, 'checkout'])->name('checkout');
        Route::post('update', [\App\Http\Controllers\SubscriptionController::class, 'update'])->name('update');
        Route::post('cancel', [\App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('resume', [\App\Http\Controllers\SubscriptionController::class, 'resume'])->name('resume');
        Route::get('billing-portal', [\App\Http\Controllers\SubscriptionController::class, 'billingPortal'])->name('billing-portal');
    });
});

// Stripe webhook route
Route::post(
    'stripe/webhook',
    [\App\Http\Controllers\StripeWebhookController::class, 'handleWebhook']
)->name('cashier.webhook');
