<?php

use App\Http\Controllers\DocumentPrintController;
use App\Http\Middleware\AllowSameOriginFrame;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect(Filament::getDefaultPanel()->getUrl());
});

Route::middleware(['auth'])->group(function () {
    Route::get('documents/{documentType}/{id}/print', [DocumentPrintController::class, 'show'])
        ->middleware(AllowSameOriginFrame::class)
        ->name('documents.print');

    // Subscription routes
    Route::prefix('subscription')->name('subscription.')->group(function () {
        Route::post('checkout', [\App\Http\Controllers\SubscriptionController::class, 'checkout'])->name('checkout');
        Route::post('update', [\App\Http\Controllers\SubscriptionController::class, 'update'])->name('update');
        Route::post('cancel', [\App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('resume', [\App\Http\Controllers\SubscriptionController::class, 'resume'])->name('resume');
        Route::get('billing-portal', [\App\Http\Controllers\SubscriptionController::class, 'billingPortal'])->name('billing-portal');
    });
});

// Stripe webhook route
Route::post(
    'stripe/webhook',
    [\App\Http\Controllers\StripeWebhookController::class, 'handleWebhook']
)->name('cashier.webhook');
