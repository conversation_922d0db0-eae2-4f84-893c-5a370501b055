## 1. Entity-Relationship Diagram (ERD)

The following diagram illustrates the relationships between the main tables in the database.

```mermaid
erDiagram
    users {
        bigint id PK
        string name
        string email
        timestamp email_verified_at
        string password
        foreignKey current_company_id
    }

    companies {
        bigint id PK
        foreignKey user_id
        string name
        boolean personal_company
    }

    company_user {
        foreignKey company_id
        foreignKey user_id
        string role
    }

    accounts {
        bigint id PK
        foreignKey company_id
        foreignKey subtype_id
        foreignKey parent_id
        string category
        string type
        string name
    }

    account_subtypes {
        bigint id PK
        foreignKey company_id
        string category
        string type
        string name
    }

    transactions {
        bigint id PK
        foreignKey company_id
        foreignKey account_id
        string description
        decimal debit
        decimal credit
    }

    bank_accounts {
        bigint id PK
        foreignKey company_id
        foreignKey account_id
        foreignKey institution_id
        string type
    }

    connected_bank_accounts {
        bigint id PK
        foreignKey company_id
        foreignKey institution_id
        foreignKey bank_account_id
        string name
        string mask
    }

    institutions {
        bigint id PK
        string name
        string website
    }

    clients {
        bigint id PK
        foreignKey company_id
        string name
    }

    vendors {
        bigint id PK
        foreignKey company_id
        string name
    }

    invoices {
        bigint id PK
        foreignKey company_id
        foreignKey client_id
        string invoice_number
        date date
        date due_date
        string status
    }

    bills {
        bigint id PK
        foreignKey company_id
        foreignKey vendor_id
        string bill_number
        date date
        date due_date
        string status
    }

    document_line_items {
        bigint id PK
        foreignKey company_id
        string documentable_type
        bigint documentable_id
        foreignKey offering_id
        decimal quantity
        bigint unit_price
    }

    offerings {
        bigint id PK
        foreignKey company_id
        string name
        integer price
    }

    users ||--o{ companies : "owns"
    users }o--o{ company_user : "member of"
    companies }o--o{ company_user : "has member"
    companies ||--o{ accounts : "has"
    companies ||--o{ account_subtypes : "has"
    companies ||--o{ transactions : "has"
    companies ||--o{ bank_accounts : "has"
    companies ||--o{ connected_bank_accounts : "has"
    companies ||--o{ clients : "has"
    companies ||--o{ vendors : "has"
    companies ||--o{ invoices : "has"
    companies ||--o{ bills : "has"
    companies ||--o{ offerings : "has"
    account_subtypes ||--o{ accounts : "classifies"
    accounts ||--o{ transactions : "has"
    accounts ||--|| bank_accounts : "is"
    institutions ||--o{ bank_accounts : "provided by"
    institutions ||--o{ connected_bank_accounts : "provided by"
    bank_accounts |o--|| connected_bank_accounts : "is connected as"
    clients ||--o{ invoices : "has"
    vendors ||--o{ bills : "has"
    invoices ||--o{ document_line_items : "has"
    bills ||--o{ document_line_items : "has"
    offerings ||--o{ document_line_items : "is"
```

## 2. Core Tables

### 2.1. `users`

Stores information about the application's users.

| Column                       | Type      | Description                                           |
| ---------------------------- | --------- | ----------------------------------------------------- |
| `id`                         | `bigint`  | Primary key.                                          |
| `name`                       | `string`  | The user's name.                                      |
| `email`                      | `string`  | The user's email address (unique).                    |
| `email_verified_at`          | `timestamp` | The timestamp when the user's email was verified.     |
| `password`                   | `string`  | The user's hashed password.                           |
| `remember_token`             | `string`  | The user's remember token for "remember me" functionality. |
| `current_company_id`         | `bigint`  | The ID of the company the user is currently accessing. |
| `current_connected_account_id` | `bigint`  | The ID of the connected account the user is currently accessing. |
| `profile_photo_path`         | `string`  | The path to the user's profile photo.                 |
| `created_at`                 | `timestamp` | The timestamp when the user was created.              |
| `updated_at`                 | `timestamp` | The timestamp when the user was last updated.         |

### 2.2. `companies`

Stores information about the companies that use the application.

| Column             | Type      | Description                                      |
| ------------------ | --------- | ------------------------------------------------ |
| `id`               | `bigint`  | Primary key.                                     |
| `user_id`          | `bigint`  | The ID of the user who owns the company.         |
| `name`             | `string`  | The name of the company.                         |
| `personal_company` | `boolean` | Whether the company is a personal company.       |
| `created_at`       | `timestamp` | The timestamp when the company was created.      |
| `updated_at`       | `timestamp` | The timestamp when the company was last updated. |

### 2.3. `company_user`

This is a pivot table that links users to companies and defines their role within the company.

| Column       | Type     | Description                               |
| -------------- | -------- | ----------------------------------------- |
| `company_id` | `bigint` | The ID of the company.                    |
| `user_id`    | `bigint` | The ID of the user.                       |
| `role`       | `string` | The user's role within the company.       |

## 3. Accounting Tables

### 3.1. `accounts`

Stores the chart of accounts for each company.

| Column        | Type      | Description                                      |
| ------------- | --------- | ------------------------------------------------ |
| `id`          | `bigint`  | Primary key.                                     |
| `company_id`  | `bigint`  | The ID of the company that owns the account.     |
| `subtype_id`  | `bigint`  | The ID of the account subtype.                   |
| `parent_id`   | `bigint`  | The ID of the parent account (for hierarchical accounts). |
| `category`    | `string`  | The account category (e.g., "asset", "liability"). |
| `type`        | `string`  | The account type (e.g., "current_asset", "long_term_liability"). |
| `code`        | `string`  | The account code.                                |
| `name`        | `string`  | The account name.                                |
| `currency_code` | `string`  | The currency code for the account.               |
| `description` | `text`    | A description of the account.                    |
| `archived`    | `boolean` | Whether the account is archived.                 |
| `default`     | `boolean` | Whether the account is a default account.        |
| `created_by`  | `bigint`  | The ID of the user who created the account.      |
| `updated_by`  | `bigint`  | The ID of the user who last updated the account. |
| `created_at`  | `timestamp` | The timestamp when the account was created.      |
| `updated_at`  | `timestamp` | The timestamp when the account was last updated. |

### 3.2. `account_subtypes`

Stores the subtypes for accounts.

| Column           | Type      | Description                               |
| ---------------- | --------- | ----------------------------------------- |
| `id`             | `bigint`  | Primary key.                              |
| `company_id`     | `bigint`  | The ID of the company that owns the subtype. |
| `multi_currency` | `boolean` | Whether the subtype supports multiple currencies. |
| `category`       | `string`  | The category of the subtype.              |
| `type`           | `string`  | The type of the subtype.                  |
| `name`           | `string`  | The name of the subtype.                  |
| `description`    | `text`    | A description of the subtype.             |
| `created_at`     | `timestamp` | The timestamp when the subtype was created. |
| `updated_at`     | `timestamp` | The timestamp when the subtype was last updated. |

### 3.3. `transactions`

Stores all financial transactions.

| Column        | Type        | Description                                      |
| ------------- | ----------- | ------------------------------------------------ |
| `id`          | `bigint`    | Primary key.                                     |
| `company_id`  | `bigint`    | The ID of the company that owns the transaction. |
| `account_id`  | `bigint`    | The ID of the account for the transaction.       |
| `description` | `string`    | A description of the transaction.                |
| `debit`       | `decimal`   | The debit amount.                                |
| `credit`      | `decimal`   | The credit amount.                               |
| `currency_code` | `string`    | The currency code for the transaction.           |
| `posted_at`   | `timestamp` | The timestamp when the transaction was posted.   |
| `created_at`  | `timestamp` | The timestamp when the transaction was created.  |
| `updated_at`  | `timestamp` | The timestamp when the transaction was last updated. |

## 4. Banking Tables

### 4.1. `bank_accounts`

Stores information about the company's bank accounts.

| Column         | Type      | Description                                      |
| -------------- | --------- | ------------------------------------------------ |
| `id`           | `bigint`  | Primary key.                                     |
| `company_id`   | `bigint`  | The ID of the company that owns the bank account. |
| `account_id`   | `bigint`  | The ID of the corresponding account in the chart of accounts. |
| `institution_id` | `bigint`  | The ID of the financial institution.             |
| `type`         | `string`  | The type of bank account (e.g., "depository").   |
| `number`       | `string`  | The bank account number.                         |
| `enabled`      | `boolean` | Whether the bank account is enabled.             |
| `created_by`   | `bigint`  | The ID of the user who created the bank account. |
| `updated_by`   | `bigint`  | The ID of the user who last updated the bank account. |
| `created_at`   | `timestamp` | The timestamp when the bank account was created. |
| `updated_at`   | `timestamp` | The timestamp when the bank account was last updated. |

### 4.2. `connected_bank_accounts`

Stores information about bank accounts that have been connected to the application via Plaid.

| Column              | Type        | Description                                      |
| ------------------- | ----------- | ------------------------------------------------ |
| `id`                | `bigint`    | Primary key.                                     |
| `company_id`        | `bigint`    | The ID of the company that owns the connected bank account. |
| `institution_id`    | `bigint`    | The ID of the financial institution.             |
| `bank_account_id`   | `bigint`    | The ID of the corresponding bank account.        |
| `external_account_id` | `string`    | The external ID of the bank account (from Plaid). |
| `access_token`      | `text`      | The access token for the connected bank account.   |
| `identifier`        | `string`    | The unique identifier for the connected bank account (from Plaid). |
| `item_id`           | `string`    | The item ID for the connected bank account (from Plaid). |
| `currency_code`     | `string`    | The currency code for the connected bank account. |
| `current_balance`   | `double`    | The current balance of the connected bank account. |
| `name`              | `string`    | The name of the connected bank account.          |
| `mask`              | `string`    | The last few digits of the bank account number.  |
| `type`              | `string`    | The type of connected bank account.              |
| `subtype`           | `string`    | The subtype of connected bank account.           |
| `import_transactions` | `boolean`   | Whether to import transactions from the connected bank account. |
| `last_imported_at`  | `timestamp` | The timestamp when transactions were last imported. |
| `created_by`        | `bigint`    | The ID of the user who created the connected bank account. |
| `updated_by`        | `bigint`    | The ID of the user who last updated the connected bank account. |
| `created_at`        | `timestamp` | The timestamp when the connected bank account was created. |
| `updated_at`        | `timestamp` | The timestamp when the connected bank account was last updated. |

## 5. Sales & Purchases Tables

### 5.1. `clients`

Stores information about the company's clients.

| Column         | Type      | Description                                      |
| -------------- | --------- | ------------------------------------------------ |
| `id`           | `bigint`  | Primary key.                                     |
| `company_id`   | `bigint`  | The ID of the company that owns the client.      |
| `name`         | `string`  | The name of the client.                          |
| `currency_code`| `string`  | The default currency for the client.             |
| `account_number`| `string`  | The client's account number.                     |
| `website`      | `string`  | The client's website.                            |
| `notes`        | `text`    | Notes about the client.                          |
| `created_by`   | `bigint`  | The ID of the user who created the client.       |
| `updated_by`   | `bigint`  | The ID of the user who last updated the client.  |
| `created_at`   | `timestamp` | The timestamp when the client was created.       |
| `updated_at`   | `timestamp` | The timestamp when the client was last updated.  |

### 5.2. `vendors`

Stores information about the company's vendors.

| Column         | Type      | Description                                      |
| -------------- | --------- | ------------------------------------------------ |
| `id`           | `bigint`  | Primary key.                                     |
| `company_id`   | `bigint`  | The ID of the company that owns the vendor.      |
| `name`         | `string`  | The name of the vendor.                          |
| `type`         | `string`  | The type of vendor.                              |
| `contractor_type`| `string`  | The contractor type.                             |
| `ssn`          | `text`    | The vendor's social security number.             |
| `ein`          | `text`    | The vendor's employer identification number.     |
| `currency_code`| `string`  | The default currency for the vendor.             |
| `account_number`| `string`  | The vendor's account number.                     |
| `website`      | `string`  | The vendor's website.                            |
| `notes`        | `text`    | Notes about the vendor.                          |
| `created_by`   | `bigint`  | The ID of the user who created the vendor.       |
| `updated_by`   | `bigint`  | The ID of the user who last updated the vendor.  |
| `created_at`   | `timestamp` | The timestamp when the vendor was created.       |
| `updated_at`   | `timestamp` | The timestamp when the vendor was last updated.  |

### 5.3. `invoices`

Stores information about invoices sent to clients.

| Column         | Type      | Description                                      |
| -------------- | --------- | ------------------------------------------------ |
| `id`           | `bigint`  | Primary key.                                     |
| `company_id`   | `bigint`  | The ID of the company that owns the invoice.     |
| `client_id`    | `bigint`  | The ID of the client the invoice is for.         |
| `invoice_number`| `string`  | The invoice number.                              |
| `date`         | `date`    | The date of the invoice.                         |
| `due_date`     | `date`    | The due date of the invoice.                     |
| `status`       | `string`  | The status of the invoice (e.g., "draft", "sent", "paid"). |
| `total`        | `bigint`  | The total amount of the invoice.                 |
| `amount_paid`  | `bigint`  | The amount paid on the invoice.                  |
| `amount_due`   | `bigint`  | The amount due on the invoice.                   |
| `created_by`   | `bigint`  | The ID of the user who created the invoice.      |
| `updated_by`   | `bigint`  | The ID of the user who last updated the invoice. |
| `created_at`   | `timestamp` | The timestamp when the invoice was created.      |
| `updated_at`   | `timestamp` | The timestamp when the invoice was last updated. |

### 5.4. `bills`

Stores information about bills received from vendors.

| Column         | Type      | Description                                      |
| -------------- | --------- | ------------------------------------------------ |
| `id`           | `bigint`  | Primary key.                                     |
| `company_id`   | `bigint`  | The ID of the company that owns the bill.        |
| `vendor_id`    | `bigint`  | The ID of the vendor the bill is from.           |
| `bill_number`  | `string`  | The bill number.                                 |
| `date`         | `date`    | The date of the bill.                            |
| `due_date`     | `date`    | The due date of the bill.                        |
| `status`       | `string`  | The status of the bill (e.g., "open", "paid").   |
| `total`        | `bigint`  | The total amount of the bill.                    |
| `amount_paid`  | `bigint`  | The amount paid on the bill.                     |
| `amount_due`   | `bigint`  | The amount due on the bill.                      |
| `created_by`   | `bigint`  | The ID of the user who created the bill.         |
| `updated_by`   | `bigint`  | The ID of the user who last updated the bill.    |
| `created_at`   | `timestamp` | The timestamp when the bill was created.         |
| `updated_at`   | `timestamp` | The timestamp when the bill was last updated.    |

### 5.5. `document_line_items`

Stores the line items for documents such as invoices and bills.

| Column            | Type      | Description                                      |
| ----------------- | --------- | ------------------------------------------------ |
| `id`              | `bigint`  | Primary key.                                     |
| `company_id`      | `bigint`  | The ID of the company that owns the line item.   |
| `documentable_type`| `string`  | The type of document the line item belongs to (e.g., "App\Models\Accounting\Invoice"). |
| `documentable_id` | `bigint`  | The ID of the document the line item belongs to. |
| `offering_id`     | `bigint`  | The ID of the offering for the line item.        |
| `description`     | `string`  | A description of the line item.                  |
| `quantity`        | `decimal` | The quantity of the line item.                   |
| `unit_price`      | `bigint`  | The unit price of the line item.                 |
| `subtotal`        | `bigint`  | The subtotal of the line item.                   |
| `total`           | `bigint`  | The total of the line item.                      |
| `tax_total`       | `bigint`  | The tax total of the line item.                  |
| `discount_total`  | `bigint`  | The discount total of the line item.             |
| `created_by`      | `bigint`  | The ID of the user who created the line item.    |
| `updated_by`      | `bigint`  | The ID of the user who last updated the line item. |
| `created_at`      | `timestamp` | The timestamp when the line item was created.    |
| `updated_at`      | `timestamp` | The timestamp when the line item was last updated. |

### 5.6. `offerings`

Stores information about the products and services that the company offers.

| Column             | Type      | Description                                      |
| ------------------ | --------- | ------------------------------------------------ |
| `id`               | `bigint`  | Primary key.                                     |
| `company_id`       | `bigint`  | The ID of the company that owns the offering.    |
| `name`             | `string`  | The name of the offering.                        |
| `description`      | `string`  | A description of the offering.                   |
| `type`             | `string`  | The type of offering (e.g., "product", "service"). |
| `price`            | `integer` | The price of the offering.                       |
| `sellable`         | `boolean` | Whether the offering can be sold.                |
| `purchasable`      | `boolean` | Whether the offering can be purchased.           |
| `income_account_id`| `bigint`  | The ID of the income account for the offering.   |
| `expense_account_id`| `bigint`  | The ID of the expense account for the offering.  |
| `created_by`       | `bigint`  | The ID of the user who created the offering.     |
| `updated_by`       | `bigint`  | The ID of the user who last updated the offering.|
| `created_at`       | `timestamp` | The timestamp when the offering was created.     |
| `updated_at`       | `timestamp` | The timestamp when the offering was last updated.|
