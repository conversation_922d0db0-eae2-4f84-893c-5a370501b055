{"name": "andrewdwallo/erpsaas", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-bcmath": "*", "ext-intl": "*", "akaunting/laravel-money": "^5.2", "andrewdwallo/filament-companies": "^4.0", "andrewdwallo/filament-selectify": "^2.0", "andrewdwallo/transmatic": "^1.1", "awcodes/filament-table-repeater": "^3.0", "barryvdh/laravel-snappy": "^1.0", "codewithdennis/filament-simple-alert": "^3.0", "fakerphp/faker": "^1.24", "filament/filament": "^3.2", "guava/filament-clusters": "^1.1", "guzzlehttp/guzzle": "^7.8", "jaocero/radio-deck": "^1.2", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "squirephp/model": "^3.4", "squirephp/repository": "^3.4", "symfony/intl": "^6.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.0", "pestphp/pest-plugin-livewire": "^3.0", "spatie/laravel-ignition": "^2.4", "spatie/laravel-ray": "^1.36"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}