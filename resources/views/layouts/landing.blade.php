<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'ERPSaaS - Complete Business Management Solution')</title>
    <meta name="description" content="@yield('description', 'Professional ERP and accounting software for growing businesses. Manage finances, invoicing, inventory, and more in one powerful platform.')">
    <meta name="keywords" content="@yield('keywords', 'ERP software, accounting software, business management, invoicing, financial reporting, inventory management, SaaS')">
    <meta name="author" content="ERPSaaS">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="@yield('title', 'ERPSaaS - Complete Business Management Solution')">
    <meta property="og:description" content="@yield('description', 'Professional ERP and accounting software for growing businesses. Manage finances, invoicing, inventory, and more in one powerful platform.')">
    <meta property="og:image" content="{{ asset('images/og-image.jpg') }}">
    <meta property="og:site_name" content="ERPSaaS">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ url()->current() }}">
    <meta property="twitter:title" content="@yield('title', 'ERPSaaS - Complete Business Management Solution')">
    <meta property="twitter:description" content="@yield('description', 'Professional ERP and accounting software for growing businesses. Manage finances, invoicing, inventory, and more in one powerful platform.')">
    <meta property="twitter:image" content="{{ asset('images/og-image.jpg') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "ERPSaaS",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('images/logo.png') }}",
        "description": "Professional ERP and accounting software for growing businesses",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "******-123-4567",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://twitter.com/erpsaas",
            "https://linkedin.com/company/erpsaas"
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "ERPSaaS",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "description": "Complete business management solution with ERP and accounting features",
        "url": "{{ url('/') }}",
        "screenshot": "{{ asset('images/app-screenshot.jpg') }}",
        "offers": {
            "@type": "Offer",
            "price": "29",
            "priceCurrency": "USD",
            "priceValidUntil": "{{ now()->addYear()->format('Y-m-d') }}",
            "availability": "https://schema.org/InStock"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "150"
        }
    }
    </script>

    <!-- Google Analytics -->
    @if(config('services.google_analytics.id'))
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config('services.google_analytics.id') }}');
    </script>
    @endif

    <!-- Facebook Pixel -->
    @if(config('services.facebook_pixel.id'))
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{ config('services.facebook_pixel.id') }}');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
        src="https://www.facebook.com/tr?id={{ config('services.facebook_pixel.id') }}&ev=PageView&noscript=1"
    /></noscript>
    @endif

    <!-- Additional head content -->
    @stack('head')
</head>
<body class="font-sans antialiased bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ route('landing.index') }}" class="flex items-center">
                        <x-application-logo class="h-8 w-auto" />
                        <span class="ml-2 text-xl font-bold text-gray-900">ERPSaaS</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('landing.features') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        Features
                    </a>
                    <a href="{{ route('landing.pricing') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        Pricing
                    </a>
                    <a href="{{ route('landing.about') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        About
                    </a>
                    <a href="{{ route('landing.contact') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        Contact
                    </a>
                    
                    <!-- Auth Links -->
                    @auth
                        <a href="{{ route('app.redirect') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                            Go to App
                        </a>
                    @else
                        <a href="{{ route('filament.company.auth.login') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                            Sign In
                        </a>
                        <a href="{{ route('filament.company.auth.register') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                            Start Free Trial
                        </a>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button type="button" class="mobile-menu-button text-gray-600 hover:text-gray-900 focus:outline-none focus:text-gray-900" aria-label="Toggle menu">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
                <a href="{{ route('landing.features') }}" class="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium">
                    Features
                </a>
                <a href="{{ route('landing.pricing') }}" class="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium">
                    Pricing
                </a>
                <a href="{{ route('landing.about') }}" class="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium">
                    About
                </a>
                <a href="{{ route('landing.contact') }}" class="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium">
                    Contact
                </a>
                
                @auth
                    <a href="{{ route('app.redirect') }}" class="bg-indigo-600 text-white block px-3 py-2 text-base font-medium rounded-md mx-3 mt-4">
                        Go to App
                    </a>
                @else
                    <a href="{{ route('filament.company.auth.login') }}" class="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium">
                        Sign In
                    </a>
                    <a href="{{ route('filament.company.auth.register') }}" class="bg-indigo-600 text-white block px-3 py-2 text-base font-medium rounded-md mx-3 mt-2">
                        Start Free Trial
                    </a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center">
                        <x-application-logo class="h-8 w-auto text-white" />
                        <span class="ml-2 text-xl font-bold">ERPSaaS</span>
                    </div>
                    <p class="mt-4 text-gray-400 max-w-md">
                        Complete business management solution for growing companies. Streamline your operations with our powerful ERP and accounting platform.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold uppercase tracking-wider">Product</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="{{ route('landing.features') }}" class="text-gray-400 hover:text-white">Features</a></li>
                        <li><a href="{{ route('landing.pricing') }}" class="text-gray-400 hover:text-white">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Security</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Integrations</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold uppercase tracking-wider">Company</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="{{ route('landing.about') }}" class="text-gray-400 hover:text-white">About</a></li>
                        <li><a href="{{ route('landing.contact') }}" class="text-gray-400 hover:text-white">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Terms</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-800">
                <p class="text-gray-400 text-sm text-center">
                    © {{ date('Y') }} ERPSaaS. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Mobile menu toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });

        // Analytics tracking function
        function trackEvent(action, category, label) {
            // Google Analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    'event_category': category,
                    'event_label': label
                });
            }

            // Facebook Pixel
            if (typeof fbq !== 'undefined') {
                fbq('track', 'Lead');
            }
        }
    </script>
    
    @stack('scripts')
</body>
</html>
