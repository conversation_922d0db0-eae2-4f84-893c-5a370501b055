<x-filament-widgets::widget>
    @if($showAlert)
        <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            @if($isOnTrial && $trialDaysRemaining <= 7)
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                            Trial Ending Soon
                        </h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Your free trial ends in {{ $trialDaysRemaining }} {{ $trialDaysRemaining === 1 ? 'day' : 'days' }}. 
                            Choose a plan to continue using ERPSaaS.
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <a href="{{ route('filament.company.pages.billing') }}" 
                           class="inline-flex items-center rounded-md bg-yellow-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-yellow-500">
                            Choose Plan
                        </a>
                    </div>
                </div>
            @elseif($status->requiresPayment())
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                            Payment Required
                        </h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Your subscription payment is {{ $status->value === 'past_due' ? 'past due' : 'required' }}. 
                            Please update your payment method to continue using ERPSaaS.
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <a href="{{ route('filament.company.pages.billing') }}" 
                           class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500">
                            Update Payment
                        </a>
                    </div>
                </div>
            @elseif($status->isExpired())
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                            Subscription Expired
                        </h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Your subscription has expired. Please reactivate your subscription to continue using ERPSaaS.
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <a href="{{ route('filament.company.pages.billing') }}" 
                           class="inline-flex items-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500">
                            Reactivate
                        </a>
                    </div>
                </div>
            @endif
        </div>
    @endif
</x-filament-widgets::widget>
