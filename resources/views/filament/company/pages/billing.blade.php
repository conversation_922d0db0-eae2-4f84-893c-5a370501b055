<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Current Subscription Status -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Current Subscription
                </h3>
                <div class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
                    @php
                        $subscriptionService = app(\App\Services\SubscriptionService::class);
                        $user = auth()->user();
                        $plan = $subscriptionService->getCurrentPlan($user);
                        $status = $subscriptionService->getSubscriptionStatus($user);
                    @endphp
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Plan</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $plan->label() }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                            <dd class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($status->color() === 'success') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                    @elseif($status->color() === 'warning') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                    @elseif($status->color() === 'danger') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                    @else bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                    @endif">
                                    {{ $status->label() }}
                                </span>
                            </dd>
                        </div>
                        @if($status === \App\Enums\SubscriptionStatus::TRIAL)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Trial Days Remaining</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ $subscriptionService->getTrialDaysRemaining($user) }} days
                                </dd>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Overview -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Usage Overview
                </h3>
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    @php
                        $currentCompanies = $user->allCompanies()->count();
                        $maxCompanies = $plan->maxCompanies();
                        $currentUsers = $user->currentCompany?->allUsers()->count() ?? 0;
                        $maxUsers = $plan->maxUsersPerCompany();
                        $currentBankAccounts = $user->currentCompany?->bankAccounts()->count() ?? 0;
                        $maxBankAccounts = $plan->maxBankAccounts();
                    @endphp
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Companies</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            {{ $currentCompanies }} / {{ $maxCompanies === 999 ? '∞' : $maxCompanies }}
                        </dd>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $maxCompanies === 999 ? 10 : min(100, ($currentCompanies / $maxCompanies) * 100) }}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Users (Current Company)</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            {{ $currentUsers }} / {{ $maxUsers === 999 ? '∞' : $maxUsers }}
                        </dd>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                            <div class="bg-green-600 h-2 rounded-full" style="width: {{ $maxUsers === 999 ? 10 : min(100, ($currentUsers / $maxUsers) * 100) }}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Bank Accounts</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            {{ $currentBankAccounts }} / {{ $maxBankAccounts === 999 ? '∞' : $maxBankAccounts }}
                        </dd>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: {{ $maxBankAccounts === 999 ? 10 : min(100, ($currentBankAccounts / $maxBankAccounts) * 100) }}%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Storage</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            0 GB / {{ $plan->maxStorageGB() }} GB
                        </dd>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: 5%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Selection Form -->
        {{ $this->form }}
    </div>
</x-filament-panels::page>
