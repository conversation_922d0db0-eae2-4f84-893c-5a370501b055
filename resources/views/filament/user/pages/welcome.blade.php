<x-filament-panels::page>
    <div class="space-y-8">
        <!-- Welcome Hero -->
        <div class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">🎉 Welcome to ERPSaaS, {{ $user->name }}!</h2>
                    <p class="text-primary-100 text-lg">
                        Your free trial is active with {{ $trialDaysRemaining }} days remaining.
                    </p>
                    <p class="text-primary-200 mt-2">
                        You have full access to all {{ $currentPlan->label() }} features during your trial.
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <x-heroicon-o-sparkles class="w-12 h-12 text-white" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Companies Section -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Your Companies</h3>
                @if($canCreateMoreCompanies)
                <a href="{{ route('filament.company.tenant.registration') }}" 
                   class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <x-heroicon-m-plus class="w-4 h-4 mr-2" />
                    Create Company
                </a>
                @endif
            </div>
            
            @if($companies->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($companies as $company)
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-semibold text-gray-900">{{ $company->name }}</h4>
                        @if($company->id === $user->current_company_id)
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Current</span>
                        @endif
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                        {{ $company->profile->email ?? 'No email set' }}
                    </p>
                    <div class="flex space-x-2">
                        <a href="{{ route('filament.company.pages.dashboard', ['tenant' => $company]) }}" 
                           class="flex-1 text-center px-3 py-2 bg-primary-600 text-white rounded text-sm hover:bg-primary-700 transition-colors">
                            Open Dashboard
                        </a>
                        @if($company->id !== $user->current_company_id)
                        <form method="POST" action="{{ route('current-company.update') }}" class="inline">
                            @csrf
                            <input type="hidden" name="company_id" value="{{ $company->id }}">
                            <button type="submit" 
                                    class="px-3 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors">
                                Switch To
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="text-center py-8">
                <x-heroicon-o-building-office class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 class="text-lg font-semibold text-gray-900 mb-2">No Companies Yet</h4>
                <p class="text-gray-600 mb-4">Create your first company to start using ERPSaaS.</p>
                <a href="{{ route('filament.company.tenant.registration') }}" 
                   class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <x-heroicon-m-plus class="w-5 h-5 mr-2" />
                    Create Your First Company
                </a>
            </div>
            @endif
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                        <x-heroicon-m-document-text class="w-4 h-4 text-primary-600" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Getting Started</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Learn how to set up your company and start managing your business.
                </p>
                <a href="#" class="text-primary-600 font-medium hover:text-primary-700">
                    View Guide →
                </a>
            </div>

            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                        <x-heroicon-m-credit-card class="w-4 h-4 text-primary-600" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Subscription</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Manage your subscription and billing preferences.
                </p>
                <a href="{{ route('filament.user.pages.billing') }}"
                   class="text-primary-600 font-medium hover:text-primary-700">
                    Manage Billing →
                </a>
            </div>

            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                        <x-heroicon-m-question-mark-circle class="w-4 h-4 text-primary-600" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Support</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Get help and support for using ERPSaaS.
                </p>
                <a href="#" class="text-primary-600 font-medium hover:text-primary-700">
                    Contact Support →
                </a>
            </div>
        </div>
    </div>
</x-filament-panels::page>
