@extends('layouts.landing')

@section('title', 'ERPSaaS - Complete Business Management Solution')
@section('description', 'Professional ERP and accounting software for growing businesses. Manage finances, invoicing, inventory, and more in one powerful platform.')

@section('content')
<!-- Test Section to verify <PERSON><PERSON><PERSON> is working -->
<div class="bg-red-500 text-white p-4 text-center">
    <h1 class="text-2xl font-bold">Tailwind Test - This should be red background with white text</h1>
</div>

<!-- Hero Section -->
<x-landing.hero
    title="Complete Business Management<br><span class='text-indigo-200'>Made Simple</span>"
    subtitle="Streamline your operations with our powerful ERP and accounting platform. Everything you need to run your business in one place."
    primary-cta="Start Free 30-Day Trial"
    secondary-cta="View Features"
    secondary-cta-url="{{ route('landing.features') }}"
/>

<!-- Features Preview -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Everything Your Business Needs
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                From accounting to inventory management, our comprehensive platform grows with your business.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach(array_slice($features, 0, 4) as $feature)
            <x-landing.feature-card
                :icon="$feature['icon']"
                :title="$feature['title']"
                :description="$feature['description']"
            />
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('landing.features') }}" 
               class="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700">
                View All Features
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- Pricing Preview -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Simple, Transparent Pricing
            </h2>
            <p class="text-xl text-gray-600">
                Choose the plan that fits your business size and needs
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            @foreach($plans as $planKey => $plan)
            <x-landing.pricing-card
                :plan="$plan"
                :popular="$plan['popular']"
            />
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('landing.pricing') }}" 
               class="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700">
                View Detailed Pricing
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-indigo-600 text-white py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Business?
        </h2>
        <p class="text-xl text-indigo-100 mb-8">
            Join thousands of businesses that trust ERPSaaS to manage their operations.
        </p>
        <a href="{{ route('filament.company.auth.register') }}" 
           class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-block">
            Start Your Free Trial Today
        </a>
        <p class="mt-4 text-indigo-200">
            30-day free trial • No setup fees • Cancel anytime
        </p>
    </div>
</section>
@endsection
