@extends('layouts.landing')

@section('title', 'Pricing - ERPSaaS')
@section('description', 'Simple, transparent pricing for ERPSaaS. Choose the plan that fits your business needs with our 30-day free trial.')

@section('content')
<!-- Hero Section -->
<section class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Simple, Transparent Pricing
        </h1>
        <p class="text-xl text-gray-600 mb-8">
            Choose the plan that fits your business size and needs. Start with a 30-day free trial.
        </p>
        
        <!-- Billing Toggle -->
        <div class="flex items-center justify-center mb-12">
            <span class="text-gray-700 mr-3">Monthly</span>
            <button id="billing-toggle" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                <span id="toggle-dot" class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1"></span>
            </button>
            <span class="text-gray-700 ml-3">Yearly</span>
            <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Save 2 months</span>
        </div>
    </div>
</section>

<!-- Pricing Cards -->
<section class="py-16 bg-white">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            @foreach($plans as $planKey => $plan)
            <div class="bg-white border-2 {{ $plan['popular'] ? 'border-indigo-500 shadow-lg' : 'border-gray-200' }} rounded-lg p-8 relative">
                @if($plan['popular'])
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                        Most Popular
                    </span>
                </div>
                @endif
                
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-gray-900">{{ $plan['name'] }}</h3>
                    <p class="text-gray-600 mt-2 mb-6">{{ $plan['description'] }}</p>
                    
                    <div class="pricing-display">
                        <div class="monthly-price">
                            <span class="text-5xl font-bold text-gray-900">${{ $plan['price_monthly'] }}</span>
                            <span class="text-gray-600 text-lg">/month</span>
                        </div>
                        <div class="yearly-price hidden">
                            <span class="text-5xl font-bold text-gray-900">${{ number_format($plan['price_yearly'] / 12, 0) }}</span>
                            <span class="text-gray-600 text-lg">/month</span>
                            <p class="text-sm text-green-600 mt-1">
                                Billed annually (${{{ $plan['price_yearly'] }}}/year)
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Everything in {{ $plan['name'] }}:</h4>
                    <ul class="space-y-3">
                        @foreach($plan['features'] as $feature)
                        <li class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="text-gray-700">{{ $feature }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>
                
                <div class="mt-8">
                    <a href="{{ route('filament.company.auth.register') }}" 
                       class="w-full {{ $plan['popular'] ? 'bg-indigo-600 hover:bg-indigo-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900' }} px-6 py-4 rounded-lg font-semibold text-center block transition-colors text-lg">
                        {{ $plan['cta'] }}
                    </a>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
            </h2>
        </div>
        
        <div class="space-y-8">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    What's included in the free trial?
                </h3>
                <p class="text-gray-600">
                    Your 30-day free trial includes full access to all Professional plan features. No credit card required to start.
                </p>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    Can I change plans later?
                </h3>
                <p class="text-gray-600">
                    Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.
                </p>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    Is my data secure?
                </h3>
                <p class="text-gray-600">
                    Absolutely. We use bank-level encryption and security measures to protect your data. Your information is backed up daily and stored securely.
                </p>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    What payment methods do you accept?
                </h3>
                <p class="text-gray-600">
                    We accept all major credit cards (Visa, MasterCard, American Express) and ACH bank transfers for annual plans.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-indigo-600 text-white py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
        </h2>
        <p class="text-xl text-indigo-100 mb-8">
            Start your free trial today and see why thousands of businesses choose ERPSaaS.
        </p>
        <a href="{{ route('filament.company.auth.register') }}" 
           class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-block">
            Start Free Trial
        </a>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggle = document.getElementById('billing-toggle');
    const toggleDot = document.getElementById('toggle-dot');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const yearlyPrices = document.querySelectorAll('.yearly-price');
    
    let isYearly = false;
    
    toggle.addEventListener('click', function() {
        isYearly = !isYearly;
        
        if (isYearly) {
            toggle.classList.add('bg-indigo-600');
            toggle.classList.remove('bg-gray-200');
            toggleDot.classList.add('translate-x-6');
            toggleDot.classList.remove('translate-x-1');
            
            monthlyPrices.forEach(price => price.classList.add('hidden'));
            yearlyPrices.forEach(price => price.classList.remove('hidden'));
        } else {
            toggle.classList.remove('bg-indigo-600');
            toggle.classList.add('bg-gray-200');
            toggleDot.classList.remove('translate-x-6');
            toggleDot.classList.add('translate-x-1');
            
            monthlyPrices.forEach(price => price.classList.remove('hidden'));
            yearlyPrices.forEach(price => price.classList.add('hidden'));
        }
    });
});
</script>
@endpush
@endsection
