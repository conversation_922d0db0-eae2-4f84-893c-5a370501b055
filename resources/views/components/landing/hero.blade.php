@props([
    'title',
    'subtitle',
    'primaryCta' => 'Start Free Trial',
    'primaryCtaUrl' => null,
    'secondaryCta' => null,
    'secondaryCtaUrl' => null,
    'note' => 'No credit card required • Full access to all features'
])

<section class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white overflow-hidden relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 relative">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                {!! $title !!}
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-indigo-100 max-w-3xl mx-auto leading-relaxed">
                {{ $subtitle }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-6">
                <a href="{{ $primaryCtaUrl ?? route('filament.company.auth.register') }}"
                   class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
                   onclick="trackEvent('cta_click', 'hero', '{{ $primaryCta }}')">
                    {{ $primaryCta }}
                </a>
                @if($secondaryCta)
                <a href="{{ $secondaryCtaUrl }}"
                   class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition-all duration-300"
                   onclick="trackEvent('cta_click', 'hero', '{{ $secondaryCta }}')">
                    {{ $secondaryCta }}
                </a>
                @endif
            </div>
            @if($note)
            <p class="text-indigo-200">
                {{ $note }}
            </p>
            @endif
        </div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-white opacity-10 rounded-full animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-white opacity-10 rounded-full animate-pulse delay-1000"></div>
    <div class="absolute top-1/2 left-20 w-12 h-12 bg-white opacity-10 rounded-full animate-pulse delay-500"></div>
</section>
