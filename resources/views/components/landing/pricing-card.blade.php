@props([
    'plan',
    'popular' => false,
    'yearly' => false
])

<div class="bg-white border-2 {{ $popular ? 'border-indigo-500 shadow-lg scale-105' : 'border-gray-200' }} rounded-lg p-8 relative transition-all duration-300 hover:shadow-lg">
    @if($popular)
    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
        <span class="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
            Most Popular
        </span>
    </div>
    @endif
    
    <div class="text-center">
        <h3 class="text-2xl font-bold text-gray-900">{{ $plan['name'] }}</h3>
        <p class="text-gray-600 mt-2 mb-6">{{ $plan['description'] }}</p>
        
        <div class="pricing-display">
            @if($yearly)
            <div class="yearly-price">
                <span class="text-5xl font-bold text-gray-900">${{ number_format($plan['price_yearly'] / 12, 0) }}</span>
                <span class="text-gray-600 text-lg">/month</span>
                <p class="text-sm text-green-600 mt-1">
                    Billed annually (${{{ $plan['price_yearly'] }}}/year)
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    Save ${{ ($plan['price_monthly'] * 12) - $plan['price_yearly'] }} per year
                </p>
            </div>
            @else
            <div class="monthly-price">
                <span class="text-5xl font-bold text-gray-900">${{ $plan['price_monthly'] }}</span>
                <span class="text-gray-600 text-lg">/month</span>
                <p class="text-sm text-gray-500 mt-1">
                    Billed monthly
                </p>
            </div>
            @endif
        </div>
    </div>
    
    <div class="mt-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">Everything in {{ $plan['name'] }}:</h4>
        <ul class="space-y-3">
            @foreach($plan['features'] as $feature)
            <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span class="text-gray-700">{{ $feature }}</span>
            </li>
            @endforeach
        </ul>
    </div>
    
    <div class="mt-8">
        <a href="{{ route('filament.company.auth.register') }}" 
           class="w-full {{ $popular ? 'bg-indigo-600 hover:bg-indigo-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900' }} px-6 py-4 rounded-lg font-semibold text-center block transition-colors text-lg">
            {{ $plan['cta'] }}
        </a>
    </div>
    
    @if($popular)
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 opacity-5 rounded-lg pointer-events-none"></div>
    @endif
</div>
