.fi-topbar > nav, .fi-sidebar-header {
    @apply bg-transparent ring-0 shadow-none !important;
    transition: background-color 0.3s, top 0.3s;
}

.fi-topbar > nav.topbar-hovered, .fi-sidebar-header.topbar-hovered {
    background-color: rgba(255, 255, 255, 0.75) !important;
}

:is(.dark .fi-topbar > nav.topbar-hovered, .dark .fi-sidebar-header.topbar-hovered) {
    @apply bg-gray-900/75 !important;
}

.fi-topbar > nav.topbar-scrolled, .fi-sidebar-header.topbar-scrolled {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

:is(.dark .fi-topbar > nav.topbar-scrolled, .dark .fi-sidebar-header.topbar-scrolled) {
    @apply bg-gray-900/50 !important;
}

/* Notification badge transparency */
.fi-topbar .fi-icon-btn-badge-ctn {
    @apply bg-transparent !important;
}
