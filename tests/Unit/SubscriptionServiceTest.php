<?php

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Models\User;
use App\Services\SubscriptionService;

beforeEach(function () {
    $this->subscriptionService = new SubscriptionService();
});

test('new user has trial plan', function () {
    $user = User::factory()->create();

    $plan = $this->subscriptionService->getCurrentPlan($user);
    $status = $this->subscriptionService->getSubscriptionStatus($user);

    expect($plan)->toBe(SubscriptionPlan::TRIAL);
    expect($status)->toBe(SubscriptionStatus::TRIAL);
});

test('user is on trial within 30 days', function () {
    $user = User::factory()->create();

    expect($this->subscriptionService->isOnTrial($user))->toBeTrue();
    expect($this->subscriptionService->isInTrialPeriod($user))->toBeTrue();
});

test('user not on trial after 30 days', function () {
    $user = User::factory()->create([
        'created_at' => now()->subDays(31),
    ]);

    expect($this->subscriptionService->isOnTrial($user))->toBeFalse();
    expect($this->subscriptionService->isInTrialPeriod($user))->toBeFalse();
});

test('trial days remaining calculation', function () {
    $user = User::factory()->create([
        'created_at' => now()->subDays(25),
    ]);

    $daysRemaining = $this->subscriptionService->getTrialDaysRemaining($user);

    expect($daysRemaining)->toBe(5);
});

test('feature access during trial', function () {
    $user = User::factory()->create();

    // All features should be available during trial
    expect($this->subscriptionService->canAccessFeature($user, 'advanced_reporting'))->toBeTrue();
    expect($this->subscriptionService->canAccessFeature($user, 'recurring_invoices'))->toBeTrue();
    expect($this->subscriptionService->canAccessFeature($user, 'multi_currency'))->toBeTrue();
    expect($this->subscriptionService->canAccessFeature($user, 'budget_management'))->toBeTrue();
    expect($this->subscriptionService->canAccessFeature($user, 'custom_branding'))->toBeTrue();
    expect($this->subscriptionService->canAccessFeature($user, 'data_export'))->toBeTrue();
});

test('subscription plan limits', function () {
    $starterPlan = SubscriptionPlan::STARTER;
    $professionalPlan = SubscriptionPlan::PROFESSIONAL;

    // Test Starter plan limits
    expect($starterPlan->maxCompanies())->toBe(1);
    expect($starterPlan->maxUsersPerCompany())->toBe(5);
    expect($starterPlan->maxMonthlyTransactions())->toBe(500);
    expect($starterPlan->maxStorageGB())->toBe(1);
    expect($starterPlan->maxBankAccounts())->toBe(2);
    expect($starterPlan->hasAdvancedReporting())->toBeFalse();

    // Test Professional plan limits
    expect($professionalPlan->maxCompanies())->toBe(3);
    expect($professionalPlan->maxUsersPerCompany())->toBe(25);
    expect($professionalPlan->maxMonthlyTransactions())->toBe(5000);
    expect($professionalPlan->maxStorageGB())->toBe(10);
    expect($professionalPlan->maxBankAccounts())->toBe(999);
    expect($professionalPlan->hasAdvancedReporting())->toBeTrue();
});

test('subscription plan pricing', function () {
    $starterPlan = SubscriptionPlan::STARTER;
    $professionalPlan = SubscriptionPlan::PROFESSIONAL;

    // Test pricing in cents
    expect($starterPlan->monthlyPrice())->toBe(2900);
    expect($starterPlan->yearlyPrice())->toBe(29000);
    expect($professionalPlan->monthlyPrice())->toBe(7900);
    expect($professionalPlan->yearlyPrice())->toBe(79000);
});
