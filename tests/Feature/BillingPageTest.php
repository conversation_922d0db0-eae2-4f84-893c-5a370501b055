<?php

use App\Models\Company;
use App\Models\User;

test('authenticated user can access billing page', function () {
    $user = User::factory()->withPersonalCompany()->create();
    $company = $user->personalCompany();

    $response = $this->actingAs($user)
        ->get("/company/{$company->id}/billing");

    $response->assertStatus(200);
});

test('unauthenticated user cannot access billing page', function () {
    $company = Company::factory()->create();

    $response = $this->get("/company/{$company->id}/billing");

    $response->assertRedirect('/company/login');
});

test('billing page shows current subscription status', function () {
    $user = User::factory()->withPersonalCompany()->create();
    $company = $user->personalCompany();

    $response = $this->actingAs($user)
        ->get("/company/{$company->id}/billing");

    $response->assertStatus(200);
    $response->assertSee('Current Subscription');
    $response->assertSee('Trial'); // New users should be on trial
});

test('billing page shows usage overview', function () {
    $user = User::factory()->withPersonalCompany()->create();
    $company = $user->personalCompany();

    $response = $this->actingAs($user)
        ->get("/company/{$company->id}/billing");

    $response->assertStatus(200);
    $response->assertSee('Usage Overview');
    $response->assertSee('Companies');
    $response->assertSee('Users');
    $response->assertSee('Storage');
});
